<template>
  <!-- 修改 -->
  <el-dialog title="修改通讯录" :visible.sync="editFormVisible" width="500px" @click="closeDialog">
    <el-form label-width="100px" :model="editForm" :rules="rules" ref="editForm">
      <el-form-item label="学校" prop="schoolId">
        <el-select v-model="editForm.schoolId" placeholder="请选择学校" style="width: 100%;" filterable @change="onSchoolChange">
          <el-option
            v-for="school in schoolList"
            :key="school.schoolId"
            :label="school.chineseName"
            :value="school.schoolId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="selectedSchoolWebsite">
        <div style="color: #909399; font-size: 12px;">
          学校网址：<el-link type="primary" :href="selectedSchoolWebsite" target="_blank">{{ selectedSchoolWebsite }}</el-link>
        </div>
      </el-form-item>
      <el-form-item label="部门" prop="department">
        <el-input v-model="editForm.department" placeholder="请输入部门"></el-input>
      </el-form-item>
      <el-form-item label="联系人姓名" prop="contactName">
        <el-input v-model="editForm.contactName" placeholder="请输入联系人姓名"></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="editForm.email" placeholder="请输入邮箱"></el-input>
      </el-form-item>
      <el-form-item label="电话" prop="phone">
        <el-input v-model="editForm.phone" placeholder="请输入电话"></el-input>
      </el-form-item>
      <el-form-item label="职位" prop="position">
        <el-input v-model="editForm.position" placeholder="请输入职位"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="submitForm('editForm')">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateAddressBook, getSchoolList } from '../../api/addressBook'

export default {
  name: 'addressBookUpdate',
  props: ['childMsg'],
  data() {
    return {
      loading: false,
      editFormVisible: false,
      schoolList: [],
      selectedSchoolWebsite: '',
      editForm: {
        id: '',
        schoolId: '',
        department: '',
        contactName: '',
        email: '',
        phone: '',
        position: '',
      },
      rules: {
        schoolId: [
          { required: true, message: '请选择学校', trigger: 'change' }
        ],
        contactName: [
          { required: true, message: '请输入联系人姓名', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        department: [
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        phone: [
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        position: [
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 获取学校列表
    getSchoolList() {
      return getSchoolList()
        .then(res => {
          this.schoolList = res.data;
        })
    },
    submitForm(editData) {
      let self = this;
      this.$refs[editData].validate(valid => {
        if (!valid) {
          return false;
        }
        self.loading = true;
        updateAddressBook(this.editForm)
          .then(res => {
            self.editFormVisible = false;
            self.loading = false;
            self.$emit('callback');
            self.$message({ type: 'success', message: '修改成功' });
          })
      });
    },
    closeDialog() {
      this.editFormVisible = false;
    },
    show(row) {
      this.editFormVisible = true;
      this.loading = false;
      this.resetForm();
      if (row) {
        Object.assign(this.editForm, row);
      }
      // 获取学校列表并设置网址
      this.getSchoolList().then(() => {
        if (row && row.schoolId) {
          const school = this.schoolList.find(s => s.schoolId === row.schoolId);
          if (school) {
            this.selectedSchoolWebsite = school.website;
          }
        }
      });
    },
    resetForm() {
      this.editForm = {
        id: '',
        schoolId: '',
        department: '',
        contactName: '',
        email: '',
        phone: '',
        position: '',
      };
      this.selectedSchoolWebsite = '';
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields();
      }
    },
    onSchoolChange(value) {
      const school = this.schoolList.find(s => s.schoolId === value);
      if (school) {
        this.selectedSchoolWebsite = school.website;
      } else {
        this.selectedSchoolWebsite = '';
      }
    }
  }
}
</script>

<style>
.el-dialog .el-dialog__body {
  padding: 0px 20px !important;
}
</style>
