<template>
  <div>
    <el-dialog title="新增职位" :visible.sync="dialogVisible" width="25%" :before-close="handleClose">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px" class="demo-ruleForm">
        <el-form-item label="职位" prop="position">
          <el-input v-model="form.position" placeholder="请输入职位"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createPosition } from '../../api/position'
import { loadToken } from '../../utils/util'

export default {
  data() {
    return {
      dialogVisible: false,
      form: {
        position: '',
        token: loadToken()
      },
      rules: {
        position: [
          { required: true, message: '请输入职位', trigger: 'blur' },
          { min: 1, max: 255, message: '长度在 1 到 255 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    show() {
      this.dialogVisible = true
      this.resetForm()
    },
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },
    resetForm() {
      this.form = {
        position: '',
        token: loadToken()
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          createPosition(this.form)
            .then(res => {
              this.$message({
                type: 'success',
                message: '新增成功!'
              })
              this.handleClose()
              this.$emit('callback')
            })
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.demo-ruleForm {
  margin: 20px;
}
</style>
