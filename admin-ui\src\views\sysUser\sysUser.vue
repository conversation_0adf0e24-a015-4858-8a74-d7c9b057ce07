<template>
  <div>
    <!-- breadcrumb -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>用户管理</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- search -->
    <el-form :inline="true" :model="formInline" class="user-search" ref="searchForm">
      <el-form-item label="查询：">
        <el-input size="small" v-model="formInline.username" placeholder="用户名" maxlength="32" clearable style="width: 160px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" icon="el-icon-search" @click="search">查询</el-button>
        <el-button size="small" type="primary" icon="el-icon-refresh" @click="resetSearch()">重置</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="popWin('sysUserInsert')">新增</el-button>
      </el-form-item>
    </el-form>

    <!--list-->
    <el-table ref="ids" @sort-change="getSortPageData" :data="listData" highlight-current-row v-loading="loading" border element-loading-text="loading" style="width: 100%;">
      <el-table-column prop="username" label="用户名" width="180" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column prop="adminFlag" label="是否管理员" width="120" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.adminFlag"
            disabled>
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="200" show-overflow-tooltip></el-table-column>
      <el-table-column align="left" label="操作" min-width="180">
        <template slot-scope="scope">
          <template v-if="scope.row.username !== 'admin'">
            <el-link>
              <i class="el-icon-key" @click="changePassword(scope.row)"> 修改密码</i>
            </el-link>
            <el-link>
              <i class="el-icon-edit" @click="popWin('sysUserUpdate', scope.row)"> 修改</i>
            </el-link>
            <el-link type="danger">
              <i class="el-icon-delete" @click="deleteSysUser(scope.row.username)"> 删除</i>
            </el-link>
          </template>
          <span v-else>系统管理员不可变更</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- page -->
    <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>

    <!-- 密码修改弹窗 -->
    <el-dialog title="修改密码" :visible.sync="passwordDialogVisible" width="25%">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="passwordForm.username" disabled></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="passwordForm.newPassword" type="password" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="passwordForm.confirmPassword" type="password" show-password></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="submitPasswordChange">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 子组件 -->
    <sysUserUpdate @callback="getPageData" ref="sysUserUpdate"></sysUserUpdate>
    <sysUserInsert @callback="getPageData" ref="sysUserInsert"></sysUserInsert>
  </div>
</template>

<script>
import { sysUserPage, sysUserDelete, changePassword, changeAdmin } from '../../api/SysUser'
import qs from 'qs';
import Pagination from '../../components/Pagination'
import { loadToken } from '../../utils/util'
import sysUserUpdate from './sysUserUpdate'
import sysUserInsert from './sysUserInsert'

export default {
  data() {
    // 校验确认密码与新密码是否一致
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      formInline: {
        current: 1,
        size: 10,
        username: undefined,
        token: loadToken(),
        orders: [],
      },
      passwordDialogVisible: false,
      passwordForm: {
        username: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 32, message: '密码长度为6-32个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      listData: [], //分页数据
      // page param
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  components: {
    Pagination,
    sysUserUpdate,
    sysUserInsert,
  },

  created() {
    // 加载用户缓存
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'));
    // 分页数据
    this.getPageData()
  },
  methods: {
    // 打开修改密码对话框
    changePassword(row) {
      this.passwordForm.username = row.username;
      this.passwordForm.newPassword = '';
      this.passwordForm.confirmPassword = '';
      this.passwordDialogVisible = true;
    },

    // 提交密码修改
    submitPasswordChange() {
      this.$refs.passwordForm.validate((valid) => {
        if (valid) {
          const params = {
            username: this.passwordForm.username,
            password: this.passwordForm.newPassword
          };

          this.loading = true;
          changePassword(params)
            .then(res => {
              this.loading = false;
              this.$message({
                type: 'success',
                message: '密码修改成功'
              });
              this.passwordDialogVisible = false;
            })
            .catch(err => {
              this.loading = false;
              this.$message({
                type: 'error',
                message: '密码修改失败，请稍后重试'
              });
              console.error(err);
            });
        }
      });
    },

    // 处理管理员状态切换
    handleAdminChange(username, isAdmin) {
      // 此方法由于已在表格中禁用管理员开关，不再被调用
      // 保留方法以便将来需要时可以重新启用
      changeAdmin({username: username})
        .then(res => {
          this.$message({type: 'success', message: '状态更新成功'});
          this.getPageData();
        })
        .catch(err => {
          // 操作失败时，恢复原状态
          this.getPageData();
          this.$message({type: 'error', message: '状态更新失败'});
        });
    },

    // page-1: page
    getPageData(parameter) {
      this.loading = true
      if (!parameter){
        parameter = this.formInline;
      }
      sysUserPage(parameter)
        .then(res => {
          this.loading = false;
          this.listData = res.data.records
          this.pageParam.currentPage = res.data.current;
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total
        })
        .catch(err => {
          this.loading = false;
          console.error('获取用户数据失败:', err);
          this.$message({
            type: 'error',
            message: '获取用户数据失败，请检查网络连接或稍后重试'
          });
        })
    },

    // page-2: callBack
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },

    // page-3: page && sort
    getSortPageData(column){
      if (column.order != null){
        let sortProp = column.prop.replace(/([A-Z])/g, "_$1").toLowerCase();
        let orderBy = {"column": sortProp, "asc": column.order == 'ascending'};
        this.formInline.orders[0] = orderBy;
      } else {
        this.formInline.orders = [];
      }
      this.getPageData();
    },

    // search-1
    search() {
      this.getPageData()
    },

    // search-2
    resetSearch() {
      this.formInline.username = undefined
    },

    // 弹窗
    popWin(windownName, data) {
      if (this.$refs[windownName]){
        this.$refs[windownName].show(data);
      }
    },

    // 删除用户
    deleteSysUser(username) {
      let _this = this;
      this.$confirm('是否删除用户['+ username +']?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        sysUserDelete({"idList": [username]})
          .then(res => {
            this.$message({type: 'success', message: '删除成功'})
            _this.getPageData();
          })
      }).catch(() => {
        this.$message({type: 'info', message: '取消删除'})
      })
    }
  }
}
</script>

<style scoped>
.user-search {
  margin-top: 20px;
}

.user-search .el-input--small {
    width: 180px!important;
}

.el-link {
  margin-left: 10px!important;
  cursor: pointer!important;
}
</style>
