2025-07-29 11:01:03.249 [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 134248
2025-07-29 11:01:03.252 [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 134426
2025-07-29 11:04:12.347 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 118592
2025-07-29 11:04:12.374 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 119049
2025-07-29 11:16:31.339 [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-29 11:16:31.339 [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-29 11:16:31.339 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-29 11:16:31.339 [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-29 11:18:18.620 [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 190146
2025-07-29 11:21:46.065 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 198173
2025-07-29 11:21:46.066 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 170096
2025-07-29 11:26:10.527 [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 248162
2025-07-29 11:26:10.528 [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 264060
2025-07-29 12:52:55.525 [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 114204
2025-07-29 12:52:55.554 [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 126101
2025-07-29 12:55:41.191 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 142294
2025-07-29 12:55:41.192 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 128987
2025-07-29 13:01:06.269 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 126225
2025-07-29 13:01:06.501 [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 172617
2025-07-29 13:02:18.959 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 69491
2025-07-29 13:02:18.987 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 69518
2025-07-29 13:03:28.685 [http-nio-20000-exec-1] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 62193
2025-07-29 13:03:28.685 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 62193
2025-07-29 13:06:16.471 [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 167277
2025-07-29 13:06:16.471 [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 167428
2025-07-29 13:11:54.278 [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 109339
2025-07-29 13:11:54.278 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 109339
2025-07-29 13:17:34.572 [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 236537
2025-07-29 13:17:34.576 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 236543
2025-07-29 13:28:02.379 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 71282
2025-07-29 13:28:02.379 [http-nio-20000-exec-1] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 71439
2025-07-29 13:29:28.048 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 84980
2025-07-29 13:29:28.074 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 85147
2025-07-29 13:31:33.957 [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 90998
2025-07-29 13:31:33.984 [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 91034
2025-07-29 15:55:27.500 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 112107
2025-07-29 15:55:27.504 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 112259
2025-07-29 16:22:54.902 [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 16:24:25.402 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 73487
2025-07-29 16:24:25.429 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 73525
2025-07-29 16:42:55.045 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 16:50:57.318 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 73190
2025-07-29 16:50:57.321 [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 73198
2025-07-29 16:53:16.121 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 127892
2025-07-29 16:53:16.124 [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 127874
2025-07-29 16:53:17.430 [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title/page, Err-detail:
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:235)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:121)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:85)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.page(Unknown Source)
	at com.my.college.service.impl.TitleServiceImpl.page(TitleServiceImpl.java:50)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:685)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$5a5351ac.page(<generated>)
	at com.my.college.controller.TitleController.page(TitleController.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor106.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor.willDoQuery(PaginationInnerInterceptor.java:141)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:75)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 70 common frames omitted
2025-07-29 16:53:27.087 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title/page, Err-detail:
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:235)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:121)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:85)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.page(Unknown Source)
	at com.my.college.service.impl.TitleServiceImpl.page(TitleServiceImpl.java:50)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:685)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$5a5351ac.page(<generated>)
	at com.my.college.controller.TitleController.page(TitleController.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor106.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor.willDoQuery(PaginationInnerInterceptor.java:141)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:75)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 70 common frames omitted
2025-07-29 16:54:40.637 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title, Err-detail:
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:243)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.my.college.service.impl.TitleServiceImpl.insert(TitleServiceImpl.java:32)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:366)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:99)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$5a5351ac.insert(<generated>)
	at com.my.college.controller.TitleController.insert(TitleController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 77 common frames omitted
2025-07-29 16:55:43.145 [http-nio-20000-exec-10] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/dept, Err-detail:
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/DeptMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.DeptMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO dept  ( dept )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
; Duplicate entry 'd1' for key 'dept.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/DeptMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.DeptMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO dept  ( dept )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
; Duplicate entry 'd1' for key 'dept.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:243)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy99.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.my.college.service.impl.DeptServiceImpl.insert(DeptServiceImpl.java:32)
	at com.my.college.service.impl.DeptServiceImpl$$FastClassBySpringCGLIB$$980fc527.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:366)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:99)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at com.my.college.service.impl.DeptServiceImpl$$EnhancerBySpringCGLIB$$3cab24cf.insert(<generated>)
	at com.my.college.controller.DeptController.insert(DeptController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 77 common frames omitted
2025-07-29 17:06:01.753 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/page, Err-detail:接口401
2025-07-29 17:06:01.753 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/page, Err-detail:接口401
2025-07-29 17:06:01.753 [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 17:06:08.028 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 110384
2025-07-29 17:06:18.796 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title, Err-detail:
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:243)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.my.college.service.impl.TitleServiceImpl.insert(TitleServiceImpl.java:32)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:366)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:99)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$d53de090.insert(<generated>)
	at com.my.college.controller.TitleController.insert(TitleController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 77 common frames omitted
2025-07-29 17:59:05.981 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-29 17:59:05.981 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-29 17:59:05.981 [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-29 17:59:05.981 [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-29 17:59:22.279 [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:84 - Err-uri:/api/title, Err-detail:标题 [aa] 已存在
2025-07-29 17:59:22.507 [http-nio-20000-exec-9] com.my.college.exception.ExceptionAspect:84 - Err-uri:/api/title, Err-detail:标题 [aa] 已存在
2025-07-29 18:00:38.451 [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/page, Err-detail:接口401
2025-07-29 18:00:38.451 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 18:00:38.451 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/page, Err-detail:接口401
2025-07-29 18:06:01.766 [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 129991
2025-07-29 18:06:01.766 [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 130002
2025-07-29 18:08:16.769 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/page, Err-detail:接口401
2025-07-29 18:08:16.769 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 18:08:16.769 [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/page, Err-detail:接口401
2025-07-29 18:08:41.306 [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-29 18:08:41.306 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-29 18:08:41.306 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-29 18:08:41.306 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-29 18:09:50.039 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 74128
2025-07-29 19:11:07.754 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 133133
2025-07-29 19:15:48.663 [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 279544
2025-07-29 19:15:48.665 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 279591
2025-07-29 19:17:37.064 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 64773
2025-07-29 19:17:37.090 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 64844
2025-07-29 19:20:55.263 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 70582
2025-07-29 19:20:55.265 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 70639
2025-07-29 19:24:11.755 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 71996
2025-07-29 19:26:17.475 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101118
2025-07-29 19:26:17.476 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 125200
2025-07-29 19:36:16.557 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 76122
2025-07-29 19:48:47.649 [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 301095
2025-07-29 19:48:47.650 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 301094
2025-07-29 19:52:25.741 [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 217540
2025-07-29 19:52:25.742 [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 217524
2025-07-29 19:55:06.067 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 156559
2025-07-29 19:55:06.068 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 156557
2025-07-29 20:01:03.156 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 151006
2025-07-29 20:01:03.157 [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 151007
2025-07-29 20:05:21.326 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 246476
2025-07-29 20:05:21.358 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 246524
2025-07-29 20:05:53.450 [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-29 20:05:53.450 [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-29 20:05:53.450 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-29 20:05:53.450 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-29 20:05:53.450 [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-29 20:05:53.450 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-29 20:06:59.135 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 71988
2025-07-30 09:14:53.636 [ main] o.springframework.boot.SpringApplication:826 - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\com\my\college\auth\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysUserServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserMapper' defined in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\com\my\college\mybatis\mapper\SysUserMapper.class]: Invocation of init method failed; nested exception is java.lang.NoClassDefFoundError: com/my/college/mybatis/entity/SysUser$SysUserBuilder
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:228)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1358)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1204)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:878)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:550)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.my.college.App.main(App.java:40)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysUserServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserMapper' defined in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\com\my\college\mybatis\mapper\SysUserMapper.class]: Invocation of init method failed; nested exception is java.lang.NoClassDefFoundError: com/my/college/mybatis/entity/SysUser$SysUserBuilder
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:885)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:789)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserMapper' defined in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\com\my\college\mybatis\mapper\SysUserMapper.class]: Invocation of init method failed; nested exception is java.lang.NoClassDefFoundError: com/my/college/mybatis/entity/SysUser$SysUserBuilder
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 31 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com/my/college/mybatis/entity/SysUser$SysUserBuilder
	at java.lang.Class.getDeclaredMethods0(Native Method)
	at java.lang.Class.privateGetDeclaredMethods(Unknown Source)
	at java.lang.Class.getDeclaredMethods(Unknown Source)
	at org.apache.ibatis.reflection.Reflector.getClassMethods(Reflector.java:280)
	at org.apache.ibatis.reflection.Reflector.addGetMethods(Reflector.java:88)
	at org.apache.ibatis.reflection.Reflector.<init>(Reflector.java:67)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(Unknown Source)
	at org.apache.ibatis.util.MapUtil.computeIfAbsent(MapUtil.java:36)
	at org.apache.ibatis.reflection.DefaultReflectorFactory.findForClass(DefaultReflectorFactory.java:44)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableFields(TableInfoHelper.java:277)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableInfo(TableInfoHelper.java:168)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableInfo(TableInfoHelper.java:144)
	at com.baomidou.mybatisplus.core.injector.AbstractSqlInjector.inspectInject(AbstractSqlInjector.java:50)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parserInjector(MybatisMapperAnnotationBuilder.java:131)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:121)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:83)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:119)
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
	at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1855)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1792)
	... 41 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.my.college.mybatis.entity.SysUser$SysUserBuilder
	at java.net.URLClassLoader.findClass(Unknown Source)
	at java.lang.ClassLoader.loadClass(Unknown Source)
	at sun.misc.Launcher$AppClassLoader.loadClass(Unknown Source)
	at java.lang.ClassLoader.loadClass(Unknown Source)
	... 62 common frames omitted
2025-07-30 09:18:21.004 [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 09:18:21.004 [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 09:18:21.004 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 09:18:21.004 [http-nio-20000-exec-9] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 09:18:21.004 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 09:18:21.004 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 09:18:46.648 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 09:18:46.648 [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 09:18:46.648 [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 09:18:46.648 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 09:18:46.648 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 09:18:46.648 [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 09:37:45.327 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 09:37:45.327 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 09:37:45.327 [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 09:37:45.327 [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 09:37:45.327 [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 09:37:45.327 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 09:58:32.677 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 09:58:32.677 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 09:58:32.677 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 09:58:32.677 [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 09:58:32.677 [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 09:58:32.677 [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 09:58:36.393 [http-nio-20000-exec-9] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 09:58:36.393 [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 09:58:36.400 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 09:58:36.400 [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 09:58:36.402 [http-nio-20000-exec-10] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 09:58:36.404 [http-nio-20000-exec-9] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 10:31:02.086 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 266558
2025-07-30 10:31:02.088 [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 266623
2025-07-30 10:34:07.118 [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 184504
2025-07-30 10:34:07.119 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 184603
2025-07-30 11:08:16.436 [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101314
2025-07-30 11:08:16.436 [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101239
2025-07-30 11:09:32.238 [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 75428
2025-07-30 11:09:32.243 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 75329
2025-07-30 11:30:55.624 [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 282331
2025-07-30 11:30:55.624 [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 282332
2025-07-30 11:33:47.844 [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 148147
2025-07-30 11:33:47.857 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 148142
2025-07-30 12:16:10.483 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 165383
2025-07-30 12:16:10.509 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 165409
2025-07-30 12:40:09.435 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 82539
2025-07-30 12:40:09.436 [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 82538
2025-07-30 12:41:07.178 [http-nio-20000-exec-5] c.m.c.f.g.onerror.CustomSearchOnError:38 - CustomSearchErrCode：网络异常, detail:java.net.SocketTimeoutException: connect timed out
2025-07-30 12:41:12.188 [http-nio-20000-exec-5] c.m.c.f.g.onerror.CustomSearchOnError:38 - CustomSearchErrCode：网络异常, detail:java.net.SocketTimeoutException: connect timed out
2025-07-30 12:58:52.113 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 12:58:52.113 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 12:58:52.113 [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 12:58:52.113 [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 12:58:52.113 [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 12:58:52.113 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 12:59:13.792 [http-nio-20000-exec-6] c.m.c.f.g.onerror.CustomSearchOnError:38 - CustomSearchErrCode：网络异常, detail:java.net.SocketTimeoutException: connect timed out
2025-07-30 12:59:18.800 [http-nio-20000-exec-6] c.m.c.f.g.onerror.CustomSearchOnError:38 - CustomSearchErrCode：网络异常, detail:java.net.SocketTimeoutException: connect timed out
2025-07-30 13:03:40.962 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 13:03:40.962 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 13:03:40.962 [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 13:03:40.962 [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 13:03:40.962 [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 13:03:40.962 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 13:42:24.062 [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 13:42:24.062 [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 13:42:24.062 [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 13:42:24.062 [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 13:42:24.062 [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 13:42:24.062 [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 13:44:32.705 [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 120627
2025-07-30 13:44:32.705 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 120627
2025-07-30 13:45:52.641 [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 67452
2025-07-30 13:45:52.706 [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 68748
2025-07-30 13:47:20.367 [ForkJoinPool.commonPool-worker-1] c.m.c.f.markdown.onerror.MarkdownOnError:26 - Markdown接口异常：java.net.SocketTimeoutException: timeout
2025-07-30 13:47:20.490 [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:47 - [1950432738494504960/1950432746241384448] markdown接口调用失败: com.my.college.exception.BusinessException: java.net.SocketTimeoutException: timeout
2025-07-30 13:47:20.730 [ForkJoinPool.commonPool-worker-2] c.m.c.f.markdown.onerror.MarkdownOnError:26 - Markdown接口异常：java.net.SocketTimeoutException: timeout
2025-07-30 13:47:20.863 [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:47 - [1950432737093607424/1950432745310248960] markdown接口调用失败: com.my.college.exception.BusinessException: java.net.SocketTimeoutException: timeout
2025-07-30 13:47:37.067 [ForkJoinPool.commonPool-worker-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 60951
2025-07-30 13:47:38.086 [ForkJoinPool.commonPool-worker-5] c.m.c.f.markdown.onerror.MarkdownOnError:26 - Markdown接口异常：java.net.SocketTimeoutException: timeout
2025-07-30 13:47:38.201 [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:47 - [1950432738494504960/1950432748300787712] markdown接口调用失败: com.my.college.exception.BusinessException: java.net.SocketTimeoutException: timeout
2025-07-30 13:50:48.033 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 83336
2025-07-30 13:50:48.059 [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 118389
2025-07-30 13:56:07.642 [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 276584
2025-07-30 13:56:07.671 [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 276659
2025-07-30 13:58:55.446 [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 124955
2025-07-30 13:58:55.446 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 124993
2025-07-30 14:00:41.087 [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 105173
2025-07-30 14:03:00.121 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 86669
2025-07-30 14:03:00.150 [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 91645
2025-07-30 14:26:48.273 [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 70342
