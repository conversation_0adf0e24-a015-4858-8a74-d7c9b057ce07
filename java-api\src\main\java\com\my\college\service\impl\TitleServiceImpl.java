package com.my.college.service.impl;

import com.my.college.mybatis.entity.Title;
import com.my.college.mybatis.mapper.TitleMapper;
import com.my.college.service.TitleService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.my.college.controller.dto.title.TitleInsertDTO;
import com.my.college.controller.dto.title.TitlePageDTO;
import com.my.college.controller.dto.title.TitleUpdateDTO;
import com.my.college.controller.dto.title.TitleDeleteDTO;
import com.my.college.controller.vo.title.TitleDetailVO;
import com.my.college.controller.vo.title.TitlePageVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.my.college.exception.BusinessException;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * 标题表 服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class TitleServiceImpl extends ServiceImpl<TitleMapper, Title> implements TitleService {


	@Transactional
	@Override
	public void insert(TitleInsertDTO insertDTO) {
		// 参数校验
		if (insertDTO == null || StrUtil.isBlank(insertDTO.getTitle())) {
			BusinessException.by("标题名称不能为空");
		}

		// 检查标题是否已存在
		Title existingTitle = this.baseMapper.selectById(insertDTO.getTitle());
		if (existingTitle != null) {
			BusinessException.by("标题 [{}] 已存在", insertDTO.getTitle());
		}

		// 创建新标题
		Title entity = BeanUtil.copyProperties(insertDTO, Title.class);
		this.save(entity);
	}

	@Transactional
	@Override
	public void update(TitleUpdateDTO updateDTO) {
		Title entity = BeanUtil.copyProperties(updateDTO, Title.class);
		this.baseMapper.updateById(entity);
	}

	@Override
	public TitleDetailVO detail(String id) {
		Title entity = this.baseMapper.selectById(id);
		return BeanUtil.copyProperties(entity, TitleDetailVO.class);
	}

	@Override
	public Page<TitlePageVO> page(TitlePageDTO pageDTO) {
		return this.baseMapper.page(pageDTO);
	}

	@Transactional
	@Override
	public void delete(TitleDeleteDTO deleteDTO) {
		this.baseMapper.deleteBatchIds(deleteDTO.getIdList());
	}

	@Override
	public List<Title> list() {
		return super.list();
	}
}
