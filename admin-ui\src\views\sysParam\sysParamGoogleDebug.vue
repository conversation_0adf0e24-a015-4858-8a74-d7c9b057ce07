<template>
  <el-card class="box-card">
    <!-- <div slot="header" class="clearfix">
      <span>Google搜索调试工具</span>
    </div> -->

    <el-form label-width="130px">
      <el-form-item label="学校">
        <div class="school-container">
          <el-select v-model="selectedSchoolId" placeholder="请选择学校" filterable clearable class="half-width"
            @change="onSchoolChange">
            <el-option v-for="school in schoolList" :key="school.schoolId" :label="school.englishName" :value="school.schoolId">
            </el-option>
          </el-select>
          <div v-if="selectedSchool && selectedSchool.website" class="school-website">
            学校网址：<el-link type="primary" :href="selectedSchool.website" target="_blank">{{ selectedSchool.website
              }}</el-link>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="标题关键词">
        <div class="checkbox-container">
          <el-checkbox-group v-model="selectedTitles" @change="onTitleSelectionChange">
            <div class="checkbox-list">
              <el-checkbox
                v-for="title in titleData"
                :key="title.title"
                :label="title.title"
                class="checkbox-item">
                {{ title.title }}
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </el-form-item>

      <el-form-item label="部门关键词">
        <div class="checkbox-container">
          <el-checkbox-group v-model="selectedDepts" @change="onDeptSelectionChange">
            <div class="checkbox-list">
              <el-checkbox
                v-for="dept in deptData"
                :key="dept.dept"
                :label="dept.dept"
                class="checkbox-item">
                {{ dept.dept }}
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </el-form-item>

      <el-form-item label="职位关键词">
        <div class="checkbox-container">
          <el-checkbox-group v-model="selectedPositions" @change="onPositionSelectionChange">
            <div class="checkbox-list">
              <el-checkbox
                v-for="position in positionData"
                :key="position.position"
                :label="position.position"
                class="checkbox-item">
                {{ position.position }}
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </el-form-item>

      <el-form-item label="谷歌关键词">
        <el-input type="textarea" v-model="googleSearchQuery" placeholder="Google搜索语句" :rows="4" readonly class="half-width">
        </el-input>
        <el-button type="primary" size="small" @click="openGoogleSearch" :disabled="!googleSearchQuery">
            在Google中打开
        </el-button>
        <!-- <el-button type="success" size="small" @click="configureToSystemParams" :disabled="!googleSearchQueryWithChineseVar || !aiUserPrompt">
          一键配置到爬虫参数
        </el-button> -->
      </el-form-item>
      
      <el-form-item label="程序化搜索谷歌">
        <el-input type="textarea" v-model="googleSearchQueryWithChineseVar" placeholder="Google搜索语句中的学校网址使用中文变量" :rows="3" readonly class="half-width">
        </el-input>
      </el-form-item>

      <el-form-item label="AI用户提示词">
        <el-input type="textarea" v-model="aiUserPrompt" placeholder="AI用户提示词" :rows="5" readonly class="half-width">
        </el-input>
        <el-button type="success" size="small" @click="configureToSystemParams" :disabled="!googleSearchQueryWithChineseVar || !aiUserPrompt">
            配置到爬虫参数
        </el-button>
      </el-form-item>

    </el-form>
  </el-card>
</template>

<script>
import { schoolList } from '../../api/school'
import { positionList } from '../../api/position'
import { titleList } from '../../api/title'
import { deptList } from '../../api/dept'
import { loadToken } from '../../utils/util'

export default {
  name: 'SysParamGoogleDebug',
  data() {
    return {
      // Google搜索调试相关数据
      schoolList: [], // 学校列表
      selectedSchool: null, // 选中的学校对象
      selectedSchoolId: null, // 选中的学校ID
      // 标题相关数据
      titleData: [], // 所有标题数据
      selectedTitles: [], // 选中的标题
      titleORJoinStr: '', // 用OR拼接的标题关键词
      // 部门相关数据
      deptData: [], // 所有部门数据
      selectedDepts: [], // 选中的部门
      deptORJoinStr: '', // 用OR拼接的部门关键词
      // 职位相关数据
      positionData: [], // 所有职位数据
      selectedPositions: [], // 选中的职位
      positionKeywords: '', // 职位关键词输入（保留用于兼容）
      positionORJoinStr: '', // 用OR拼接的职位关键词
      googleSearchQuery: '', // Google搜索语句
      googleSearchQueryWithChineseVar: '', // Google搜索语句，其中学校网址使用中文变量
      aiUserPrompt: '', // AI用户提示词
      baseAiUserPrompt: '', // AI用户提示词基础模板
      // 示例职位列表
      examplePositions: [
        'Education Abroad Coordinator', 'global experience director',
        'study abroad director', 'international program director',
        'faculty-led program manager', 'International Student Advisor',
        'Study Abroad Advisor', 'Global Programs Manager'
      ]
    }
  },

  created() {
    // 加载学校列表
    this.loadSchoolList();
    // 加载标题列表
    this.loadTitleList();
    // 加载部门列表
    this.loadDeptList();
    // 加载职位列表
    this.loadPositionList();
    // 初始化AI用户提示词
    this.initAiUserPrompt();
  },



  methods: {
    // 加载学校列表
    loadSchoolList() {
      const params = {
        token: loadToken()
      };

      schoolList(params).then(res => {
        if (res.success && res.data) {
          this.schoolList = res.data;
          console.log('学校列表数据:', this.schoolList);
          console.log('学校列表长度:', this.schoolList.length);
          if (this.schoolList.length > 0) {
            console.log('第一个学校:', this.schoolList[0]);
          }
        } else {
          this.$message.error('获取学校列表失败');
        }
      });
    },

    // 加载标题列表
    loadTitleList() {
      const params = {
        token: loadToken()
      };

      titleList(params).then(res => {
        if (res.success && res.data) {
          this.titleData = res.data;
        } else {
          this.$message.error('获取标题列表失败');
        }
      });
    },

    // 加载部门列表
    loadDeptList() {
      const params = {
        token: loadToken()
      };

      deptList(params).then(res => {
        if (res.success && res.data) {
          this.deptData = res.data;
        } else {
          this.$message.error('获取部门列表失败');
        }
      });
    },

    // 加载职位列表
    loadPositionList() {
      const params = {
        token: loadToken()
      };

      positionList(params).then(res => {
        if (res.success && res.data) {
          // 保存原始数据格式
          this.positionData = res.data;
          // 职位数据加载完成后，初始化默认选中
          this.initDefaultPositions();
        } else {
          this.$message.error('获取职位列表失败');
        }
      });
    },

    // 初始化默认选中的职位
    initDefaultPositions() {
      const defaultPositions = [
        'Education Abroad Coordinator',
        'global experience director',
        'study abroad director',
        'international program director',
        'faculty-led program manager'
      ];

      // 设置默认选中的职位（只选择存在于职位数据中的）
      this.selectedPositions = defaultPositions.filter(pos =>
        this.positionData.some(item => item.position === pos)
      );
      // 生成positionORJoinStr
      this.generatePositionORJoinStr();
    },

    // 初始化AI用户提示词
    initAiUserPrompt() {
      // 基础提示词模板
      this.baseAiUserPrompt = `Here is the content:<url_content>\${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.</user_request><schema_block>{  "properties": {    "contactName": {       "description": "The contactName of the entity.",      "title": "contactName",      "type": "string" },    "department": {      "description": "The department of the entity.",      "title": "department",      "type": "string"    },    "position": {      "description": "The position of the  entity.",      "title": "position",      "type": "string"    },    "email": {      "description": "The email of the entity.",      "title": "email",      "type": "string"    },    "phone": {      "description": "The phone of the entity.",      "title": "phone",      "type": "string"    }  },  "required": [    "contactName" ],  "title": "BusinessData",  "type": "object"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using "//" or  "#" in the JSON output. - Do NOT using "\`\`\`json" and "\`\`\`" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.`;

      // 初始化时更新AI用户提示词
      this.updateAiUserPrompt();
    },

    // 更新AI用户提示词
    updateAiUserPrompt() {
      let prompt = this.baseAiUserPrompt;
      let replacement = "";

      // 如果有职位限制
      if (this.deptORJoinStr) {
        const deptRestriction = `仅抓取以下部门:${this.deptORJoinStr} `;
        replacement += deptRestriction;
      }

      // 如果有职位限制
      if (this.positionORJoinStr) {
        const positionRestriction = `仅抓取以下职位:${this.positionORJoinStr}`;
        replacement += positionRestriction;
      }
      
      // 在</user_request>前添加部门、职位限制文本
      if (replacement){
        prompt = prompt.replace('</user_request>', `${replacement}</user_request>`);
      }

      this.aiUserPrompt = prompt;
    },

    // 学校选择变化处理
    onSchoolChange() {
      // 根据选中的ID找到对应的学校对象
      if (this.selectedSchoolId) {
        this.selectedSchool = this.schoolList.find(school => school.schoolId === this.selectedSchoolId);
      } else {
        this.selectedSchool = null;
      }

      console.log('选中的学校ID:', this.selectedSchoolId);
      console.log('选中的学校对象:', this.selectedSchool);
      console.log('学校英文名称:', this.selectedSchool && this.selectedSchool.englishName);
      console.log('学校网址:', this.selectedSchool && this.selectedSchool.website);
      this.generateGoogleSearchQuery();
    },

    // 标题选择变化处理
    onTitleSelectionChange() {
      this.generateTitleORJoinStr();
    },

    // 部门选择变化处理
    onDeptSelectionChange() {
      this.generateDeptORJoinStr();
    },

    // 职位选择变化处理
    onPositionSelectionChange() {
      this.generatePositionORJoinStr();
    },

    // 生成titleORJoinStr
    generateTitleORJoinStr() {
      if (this.selectedTitles.length === 0) {
        this.titleORJoinStr = '';
      } else {
        // 生成"标题XXX" OR "标题XXX"格式
        this.titleORJoinStr = this.selectedTitles.map(title => `"${title}"`).join(' OR ');
      }

      // 生成Google搜索语句
      this.generateGoogleSearchQuery();

      // 更新AI用户提示词
      this.updateAiUserPrompt();
    },

    // 生成deptORJoinStr
    generateDeptORJoinStr() {
      if (this.selectedDepts.length === 0) {
        this.deptORJoinStr = '';
      } else {
        // 生成"部门XXX" OR "部门XXX"格式
        this.deptORJoinStr = this.selectedDepts.map(dept => `"${dept}"`).join(' OR ');
      }

      // 生成Google搜索语句
      this.generateGoogleSearchQuery();

      // 更新AI用户提示词
      this.updateAiUserPrompt();
    },

    // 生成positionORJoinStr
    generatePositionORJoinStr() {
      if (this.selectedPositions.length === 0) {
        this.positionORJoinStr = '';
      } else {
        // 生成"职位XXX" OR "职位XXX"格式
        this.positionORJoinStr = this.selectedPositions.map(position => `"${position}"`).join(' OR ');
      }

      // 生成Google搜索语句
      this.generateGoogleSearchQuery();

      // 更新AI用户提示词
      this.updateAiUserPrompt();
    },

    // 职位列表输入变化处理（保留用于兼容）
    onPositionKeywordsChange() {
      this.processPositionKeywords();
    },

    // 从标签添加职位
    addPositionFromTag(position) {
      // 检查是否已选中
      if (!this.selectedPositions.includes(position)) {
        // 检查职位是否在可选列表中
        const positionExists = this.positionData.some(item => item.position === position);
        if (positionExists) {
          // 添加到选中列表
          this.selectedPositions.push(position);
          // 触发变化处理
          this.onPositionSelectionChange();
        } else {
          this.$message.warning(`职位 "${position}" 不在可选列表中`);
        }
      }
    },

    // 处理职位列表
    processPositionKeywords() {
      if (!this.positionKeywords.trim()) {
        this.positionORJoinStr = '';
        this.googleSearchQuery = '';
        this.googleSearchQueryWithChineseVar = '';
        // 更新AI用户提示词（清空职位限制）
        this.updateAiUserPrompt();
        return;
      }

      // 按中英文逗号分隔，去除空白字符
      const keywords = this.positionKeywords
        .split(/[,，]/)
        .map(keyword => keyword.trim())
        .filter(keyword => keyword.length > 0);

      if (keywords.length === 0) {
        this.positionORJoinStr = '';
        this.googleSearchQuery = '';
        this.googleSearchQueryWithChineseVar = '';
        // 更新AI用户提示词（清空职位限制）
        this.updateAiUserPrompt();
        return;
      }

      // 生成"职位XXX" OR "职位XXX"格式
      const positionList = keywords.map(keyword => `"${keyword}"`).join(' OR ');
      this.positionORJoinStr = positionList;

      // 生成Google搜索语句
      this.generateGoogleSearchQuery();

      // 更新AI用户提示词
      this.updateAiUserPrompt();
    },

    // 提取域名
    extractDomain(website) {
      if (!website) return '';

      try {
        // 移除协议前缀
        let domain = website.replace(/^https?:\/\//, '');
        // 移除路径
        domain = domain.split('/')[0];
        // 移除www前缀
        domain = domain.replace(/^www\./, '');

        // 如果包含.edu，优先使用.edu域名
        if (domain.includes('.edu')) {
          const parts = domain.split('.');
          const eduIndex = parts.findIndex(part => part === 'edu');
          if (eduIndex > 0) {
            return parts.slice(eduIndex - 1, eduIndex + 1).join('.');
          }
        }

        return domain;
      } catch (error) {
        return website;
      }
    },

    // 生成Google搜索语句
    generateGoogleSearchQuery() {
      if (!this.selectedSchool || !this.selectedSchool.website) {
        this.googleSearchQuery = '';
        this.googleSearchQueryWithChineseVar = '';
        return;
      }

      const schoolDomain = this.extractDomain(this.selectedSchool.website);

      // 生成标题关键词字符串
      let titleStr = '';
      if (this.titleORJoinStr) {
        titleStr = `(intitle:${this.titleORJoinStr})`;
      }

      // 生成部门关键词字符串
      let deptStr = '';
      if (this.deptORJoinStr) {
        deptStr = `(intext:${this.deptORJoinStr})`;
      }

      // 如果没有任何关键词，清空搜索语句
      if (!titleStr && !deptStr) {
        this.googleSearchQuery = '';
        this.googleSearchQueryWithChineseVar = '';
        return;
      }

      const template = `site:${schoolDomain} ${titleStr} ${deptStr} (intext:"@${schoolDomain}" OR intext:"email" OR intext:"contact") -inurl:("archive" | "old" | "pdf") -filetype:pdf -filetype:xlsx -filetype:doc`;

      this.googleSearchQuery = template;

      const template2 = `site:学校网址 ${titleStr} ${deptStr} (intext:"@学校网址" OR intext:"email" OR intext:"contact") -inurl:("archive" | "old" | "pdf") -filetype:pdf -filetype:xlsx -filetype:doc`;
      this.googleSearchQueryWithChineseVar = template2;
    },

    // 一键配置到系统参数
    configureToSystemParams() {
      if (!this.googleSearchQueryWithChineseVar || !this.aiUserPrompt) {
        this.$message.warning('请先选择学校和关键词');
        return;
      }

      // 通过事件向父组件传递数据
      this.$emit('configure-to-system-params', {
        googleKeyword: this.googleSearchQueryWithChineseVar,
        aiUserPrompt: this.aiUserPrompt
      });
    },

    // 在Google中搜索
    openGoogleSearch() {
      if (!this.googleSearchQuery) {
        this.$message.warning('没有可搜索的内容');
        return;
      }

      const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(this.googleSearchQuery)}`;
      window.open(searchUrl, '_blank');
    },


  }
}
</script>

<style scoped>
.half-width {
  width: 43%;
}

.form-item-container {
  display: flex;
  align-items: center;
}

/* 学校选择容器样式 */
.school-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 学校网址样式 */
.school-website {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
}

/* 复选框容器样式 */
.checkbox-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 5px;
  background-color: #fff;
}

/* 复选框列表样式 */
.checkbox-list {
  display: flex;
  flex-direction: column;
}

/* 复选框项样式 */
.checkbox-item {
  margin: 2px 0;
  display: block;
  line-height: 1.2;
}

/* Element UI 复选框样式优化 */
.checkbox-container .el-checkbox {
  margin-right: 0;
  margin-bottom: 3px;
  height: auto;
}

.checkbox-container .el-checkbox__label {
  padding-left: 8px;
  font-size: 13px;
  line-height: 1.3;
}

/* 减少表单项之间的间距 */
.el-form-item {
  margin-bottom: 15px;
}


</style>

<style>
/* 重置下拉框选项的颜色 - 使用更强的选择器和优先级 */
.el-select-dropdown .el-select-dropdown__item,
.el-select-dropdown__item,
.el-popper .el-select-dropdown__item {
  color: #606266 !important;
}

.el-select-dropdown .el-select-dropdown__item.selected,
.el-select-dropdown__item.selected,
.el-popper .el-select-dropdown__item.selected {
  color: #606266 !important;
  background-color: #ffffff !important;
  font-weight: normal !important;
}

.el-select-dropdown .el-select-dropdown__item:hover,
.el-select-dropdown__item:hover,
.el-popper .el-select-dropdown__item:hover {
  color: #606266 !important;
  background-color: #e6f7ff !important;
}

.el-select-dropdown .el-select-dropdown__item.selected:hover,
.el-select-dropdown__item.selected:hover,
.el-popper .el-select-dropdown__item.selected:hover {
  color: #606266 !important;
  background-color: #e6f7ff !important;
  font-weight: normal !important;
}

/* 额外的覆盖规则，针对可能的其他状态 */
.el-select-dropdown__item span,
.el-select-dropdown__item .el-select-dropdown__item-text {
  color: #606266 !important;
}

.el-select-dropdown__item:hover span,
.el-select-dropdown__item:hover .el-select-dropdown__item-text {
  color: #606266 !important;
}

.el-select-dropdown__item.selected span,
.el-select-dropdown__item.selected .el-select-dropdown__item-text {
  color: #606266 !important;
  font-weight: normal !important;
}



</style>
