package com.my.college.service;

import com.my.college.mybatis.entity.Dept;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.my.college.controller.dto.dept.DeptInsertDTO;
import com.my.college.controller.dto.dept.DeptPageDTO;
import com.my.college.controller.dto.dept.DeptUpdateDTO;
import com.my.college.controller.dto.dept.DeptDeleteDTO;
import com.my.college.controller.vo.dept.DeptDetailVO;
import com.my.college.controller.vo.dept.DeptPageVO;

/**
 * 部门表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface DeptService extends IService<Dept> {

	/**
	 * 新增
	 */
	void insert(DeptInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(DeptUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	DeptDetailVO detail(String id);

	/**
	 * 分页
	 */
	Page<DeptPageVO> page(DeptPageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(DeptDeleteDTO deleteDTO);

	/**
	 * 查询全部部门
	 */
	List<Dept> list();

}
