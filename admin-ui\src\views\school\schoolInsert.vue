<template>
  <!-- 新增 -->
  <el-dialog title="新增学校" :visible.sync="editFormVisible" width="500px" @click="closeDialog">
    <el-form label-width="100px" :model="editForm" :rules="rules" ref="editForm">
      <el-form-item label="省份" prop="provinceId">
        <el-select v-model="editForm.provinceId" placeholder="请选择省份" clearable filterable style="width: 100%;">
          <el-option
            v-for="item in provinceList"
            :key="item.provinceId"
            :label="`${item.provinceId} - ${item.chineseName}`"
            :value="item.provinceId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="中文名称" prop="chineseName">
        <el-input v-model="editForm.chineseName" placeholder="请输入中文名称"></el-input>
      </el-form-item>
      <el-form-item label="英文名称" prop="englishName">
        <el-input v-model="editForm.englishName" placeholder="请输入英文名称"></el-input>
      </el-form-item>
      <el-form-item label="学校网址" prop="website">
        <el-input v-model="editForm.website" placeholder="请输入学校网址"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="editForm.remark" placeholder="请输入备注" type="textarea" :rows="3"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="submitForm('editForm')">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createSchool } from '../../api/school'
import { provinceList } from '../../api/province'

export default {
  name: 'schoolInsert',
  props: ['childMsg'],
  data() {
    return {
      loading: false,
      editFormVisible: false,
      editForm: {
        provinceId: '',
        chineseName: '',
        englishName: '',
        website: '',
        remark: ''
      },
      provinceList: [], //省份列表
      rules: {
        chineseName: [
          { required: true, message: '请输入中文名称', trigger: 'blur' },
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        website: [
          { required: true, message: '请输入学校网址', trigger: 'blur' },
          { pattern: /^(http|https):\/\/[^\s]+$/, message: '网址必须是http或https开头的标准URL格式', trigger: 'blur' },
          { max: 300, message: '长度不能超过300个字符', trigger: 'blur' }
        ],
        remark: [
          { max: 300, message: '长度不能超过300个字符', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    // 加载省份列表
    this.loadProvinceList();
  },
  methods: {
    // 加载省份列表
    loadProvinceList() {
      provinceList().then(res => {
        this.provinceList = res.data || [];
      });
    },
    submitForm(editData) {
      let self = this;
      this.$refs[editData].validate(valid => {
        if (!valid) {
          return false;
        }
        self.loading = true;
        createSchool(this.editForm)
          .then(res => {
            self.editFormVisible = false;
            self.loading = false;
            self.$emit('callback');
            self.$message({ type: 'success', message: '新增成功' });
          });
      });
    },
    closeDialog() {
      this.editFormVisible = false;
    },
    show() {
      this.editFormVisible = true;
      this.loading = false;
      this.resetForm();
    },
    resetForm() {
      this.editForm = {
        provinceId: '',
        chineseName: '',
        englishName: '',
        website: '',
        remark: ''
      };
      if (this.$refs.editForm) {
        this.$refs.editForm.resetFields();
      }
    }
  }
}
</script>

<style>
.el-dialog .el-dialog__body {
  padding: 0px 20px !important;
}
</style>
