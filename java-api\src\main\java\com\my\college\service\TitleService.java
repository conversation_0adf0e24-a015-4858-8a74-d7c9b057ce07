package com.my.college.service;

import com.my.college.mybatis.entity.Title;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.my.college.controller.dto.title.TitleInsertDTO;
import com.my.college.controller.dto.title.TitlePageDTO;
import com.my.college.controller.dto.title.TitleUpdateDTO;
import com.my.college.controller.dto.title.TitleDeleteDTO;
import com.my.college.controller.vo.title.TitleDetailVO;
import com.my.college.controller.vo.title.TitlePageVO;

/**
 * 标题表 服务类
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface TitleService extends IService<Title> {

	/**
	 * 新增
	 */
	void insert(TitleInsertDTO insertDTO);

	/**
	 * 修改
	 */
	void update(TitleUpdateDTO updateDTO);

	/**
	 * 查询详情
	 * @param id 主键
	 */
	TitleDetailVO detail(String id);

	/**
	 * 分页
	 */
	Page<TitlePageVO> page(TitlePageDTO pageDTO);	

	/**
	 * 批量删除(物理删除)
	 */
	void delete(TitleDeleteDTO deleteDTO);

	/**
	 * 查询全部标题
	 */
	List<Title> list();

}
