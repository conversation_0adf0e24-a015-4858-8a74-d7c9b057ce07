2025-07-29 10:29:08.538 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 10:29:08.544 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,ali<PERSON>-<PERSON>,ali<PERSON>-db,aliyun-forest
2025-07-29 10:29:09.416 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 10:29:09.417 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 10:29:09.417 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [<PERSON>] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 10:29:09.423 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 10:29:12.193 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 10:29:13.894 INFO [ main] com.my.college.App:61 - Started App in 6.314 seconds (JVM running for 7.574)
2025-07-29 10:29:13.907 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 11:01:03.249 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 134248
2025-07-29 11:01:03.252 ERROR [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 134426
2025-07-29 11:04:12.347 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 118592
2025-07-29 11:04:12.374 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 119049
2025-07-29 11:15:03.916 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 11:15:03.922 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 11:15:05.073 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 11:15:05.073 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 11:15:05.073 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 11:15:05.073 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 11:15:08.140 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 11:15:09.575 INFO [ main] com.my.college.App:61 - Started App in 6.704 seconds (JVM running for 8.35)
2025-07-29 11:15:09.586 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 11:16:31.339 ERROR [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-29 11:16:31.339 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-29 11:16:31.339 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-29 11:16:31.339 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-29 11:18:18.620 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 190146
2025-07-29 11:21:46.065 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 198173
2025-07-29 11:21:46.066 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 170096
2025-07-29 11:26:10.527 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 248162
2025-07-29 11:26:10.528 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 264060
2025-07-29 12:52:55.525 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 114204
2025-07-29 12:52:55.554 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 126101
2025-07-29 12:55:41.191 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 142294
2025-07-29 12:55:41.192 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 128987
2025-07-29 13:01:06.269 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 126225
2025-07-29 13:01:06.501 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 172617
2025-07-29 13:02:18.959 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 69491
2025-07-29 13:02:18.987 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 69518
2025-07-29 13:03:28.685 ERROR [http-nio-20000-exec-1] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 62193
2025-07-29 13:03:28.685 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 62193
2025-07-29 13:06:16.471 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 167277
2025-07-29 13:06:16.471 ERROR [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 167428
2025-07-29 13:11:54.278 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 109339
2025-07-29 13:11:54.278 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 109339
2025-07-29 13:17:34.572 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 236537
2025-07-29 13:17:34.576 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 236543
2025-07-29 13:28:02.379 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 71282
2025-07-29 13:28:02.379 ERROR [http-nio-20000-exec-1] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 71439
2025-07-29 13:29:28.048 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 84980
2025-07-29 13:29:28.074 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 85147
2025-07-29 13:31:33.957 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 90998
2025-07-29 13:31:33.984 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 91034
2025-07-29 15:55:27.500 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 112107
2025-07-29 15:55:27.504 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 112259
2025-07-29 16:22:29.134 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 16:22:29.141 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 16:22:30.499 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 16:22:30.500 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 16:22:30.500 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 16:22:30.500 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 16:22:33.523 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 16:22:34.856 INFO [ main] com.my.college.App:61 - Started App in 6.948 seconds (JVM running for 13.997)
2025-07-29 16:22:34.866 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 16:22:54.902 ERROR [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 16:24:25.402 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 73487
2025-07-29 16:24:25.429 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 73525
2025-07-29 16:30:37.383 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 16:30:37.389 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 16:30:38.343 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 16:30:38.343 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 16:30:38.343 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 16:30:38.343 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 16:30:41.168 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 16:30:42.603 INFO [ main] com.my.college.App:61 - Started App in 6.128 seconds (JVM running for 7.34)
2025-07-29 16:30:42.613 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 16:42:55.045 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 16:50:57.318 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 73190
2025-07-29 16:50:57.321 ERROR [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 73198
2025-07-29 16:53:16.121 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 127892
2025-07-29 16:53:16.124 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 127874
2025-07-29 16:53:17.430 ERROR [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title/page, Err-detail:
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:235)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:121)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:85)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.page(Unknown Source)
	at com.my.college.service.impl.TitleServiceImpl.page(TitleServiceImpl.java:50)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:685)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$5a5351ac.page(<generated>)
	at com.my.college.controller.TitleController.page(TitleController.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor106.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor.willDoQuery(PaginationInnerInterceptor.java:141)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:75)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 70 common frames omitted
2025-07-29 16:53:27.087 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title/page, Err-detail:
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
### The error may exist in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\mapper\TitleMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) FROM title AS t1 WHERE 1 = 1
### Cause: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:235)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForIPage(MybatisMapperMethod.java:121)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:85)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.page(Unknown Source)
	at com.my.college.service.impl.TitleServiceImpl.page(TitleServiceImpl.java:50)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:685)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$5a5351ac.page(<generated>)
	at com.my.college.controller.TitleController.page(TitleController.java:66)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:634)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLSyntaxErrorException: Table 'college-leader-address-book.title' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at sun.reflect.GeneratedMethodAccessor106.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.query(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor.willDoQuery(PaginationInnerInterceptor.java:141)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:75)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor121.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 70 common frames omitted
2025-07-29 16:54:40.637 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title, Err-detail:
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:243)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.my.college.service.impl.TitleServiceImpl.insert(TitleServiceImpl.java:32)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:366)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:99)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$5a5351ac.insert(<generated>)
	at com.my.college.controller.TitleController.insert(TitleController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 77 common frames omitted
2025-07-29 16:55:43.145 ERROR [http-nio-20000-exec-10] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/dept, Err-detail:
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/DeptMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.DeptMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO dept  ( dept )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
; Duplicate entry 'd1' for key 'dept.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/DeptMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.DeptMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO dept  ( dept )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
; Duplicate entry 'd1' for key 'dept.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:243)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy99.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.my.college.service.impl.DeptServiceImpl.insert(DeptServiceImpl.java:32)
	at com.my.college.service.impl.DeptServiceImpl$$FastClassBySpringCGLIB$$980fc527.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:366)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:99)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at com.my.college.service.impl.DeptServiceImpl$$EnhancerBySpringCGLIB$$3cab24cf.insert(<generated>)
	at com.my.college.controller.DeptController.insert(DeptController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'd1' for key 'dept.PRIMARY'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.GeneratedMethodAccessor103.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 77 common frames omitted
2025-07-29 17:04:12.157 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 17:04:12.162 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 17:04:13.191 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 17:04:13.191 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 17:04:13.191 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 17:04:13.191 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 17:04:17.066 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 17:04:18.829 INFO [ main] com.my.college.App:61 - Started App in 7.594 seconds (JVM running for 8.833)
2025-07-29 17:04:18.841 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 17:06:01.753 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/page, Err-detail:接口401
2025-07-29 17:06:01.753 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/page, Err-detail:接口401
2025-07-29 17:06:01.753 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 17:06:08.028 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 110384
2025-07-29 17:06:18.796 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:172 - Err-uri:/api/title, Err-detail:
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
org.springframework.dao.DuplicateKeyException: 
### Error updating database.  Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
### The error may exist in com/my/college/mybatis/mapper/TitleMapper.java (best guess)
### The error may involve com.my.college.mybatis.mapper.TitleMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO title  ( title )  VALUES  ( ? )
### Cause: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
; Duplicate entry 'aa' for key 'title.PRIMARY'; nested exception is java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:243)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at com.sun.proxy.$Proxy83.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at com.sun.proxy.$Proxy113.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.IService.save(IService.java:63)
	at com.my.college.service.impl.TitleServiceImpl.insert(TitleServiceImpl.java:32)
	at com.my.college.service.impl.TitleServiceImpl$$FastClassBySpringCGLIB$$d95511a6.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:769)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:366)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:99)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:747)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:689)
	at com.my.college.service.impl.TitleServiceImpl$$EnhancerBySpringCGLIB$$d53de090.insert(<generated>)
	at com.my.college.controller.TitleController.insert(TitleController.java:39)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:106)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:888)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:793)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at cn.dev33.satoken.filter.SaPathCheckFilterForServlet.doFilter(SaPathCheckFilterForServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:367)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:860)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1598)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(Unknown Source)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(Unknown Source)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Unknown Source)
Caused by: java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'aa' for key 'title.PRIMARY'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:117)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:497)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy90.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:64)
	at com.sun.proxy.$Proxy88.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy87.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:194)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:181)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(Unknown Source)
	at java.lang.reflect.Method.invoke(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 77 common frames omitted
2025-07-29 17:06:37.077 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 17:06:37.083 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 17:06:38.053 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 17:06:38.053 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 17:06:38.054 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 17:06:38.054 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 17:06:41.882 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 17:06:43.463 INFO [ main] com.my.college.App:61 - Started App in 7.311 seconds (JVM running for 8.506)
2025-07-29 17:06:43.473 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 17:59:05.981 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-29 17:59:05.981 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-29 17:59:05.981 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-29 17:59:05.981 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-29 17:59:22.279 ERROR [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:84 - Err-uri:/api/title, Err-detail:标题 [aa] 已存在
2025-07-29 17:59:22.507 ERROR [http-nio-20000-exec-9] com.my.college.exception.ExceptionAspect:84 - Err-uri:/api/title, Err-detail:标题 [aa] 已存在
2025-07-29 18:00:32.131 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 18:00:32.137 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 18:00:33.032 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 18:00:33.032 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 18:00:33.032 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 18:00:33.032 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 18:00:36.302 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 18:00:37.720 INFO [ main] com.my.college.App:61 - Started App in 6.544 seconds (JVM running for 13.536)
2025-07-29 18:00:37.732 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 18:00:38.451 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/page, Err-detail:接口401
2025-07-29 18:00:38.451 ERROR [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/page, Err-detail:接口401
2025-07-29 18:00:38.451 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 18:06:01.766 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 129991
2025-07-29 18:06:01.766 ERROR [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 130002
2025-07-29 18:08:07.807 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 18:08:07.813 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 18:08:08.678 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 18:08:08.678 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 18:08:08.678 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 18:08:08.679 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 18:08:11.431 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 18:08:12.760 INFO [ main] com.my.college.App:61 - Started App in 5.911 seconds (JVM running for 7.103)
2025-07-29 18:08:12.770 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 18:08:16.769 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/page, Err-detail:接口401
2025-07-29 18:08:16.769 ERROR [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/page, Err-detail:接口401
2025-07-29 18:08:16.769 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/page, Err-detail:接口401
2025-07-29 18:08:31.920 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 18:08:31.925 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 18:08:32.802 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 18:08:32.802 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 18:08:32.803 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 18:08:32.803 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 18:08:35.570 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 18:08:36.934 INFO [ main] com.my.college.App:61 - Started App in 5.919 seconds (JVM running for 7.135)
2025-07-29 18:08:36.944 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 18:08:41.306 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-29 18:08:41.306 ERROR [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-29 18:08:41.306 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-29 18:08:41.306 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-29 18:09:50.039 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 74128
2025-07-29 19:11:07.754 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 133133
2025-07-29 19:15:48.663 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 279544
2025-07-29 19:15:48.665 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 279591
2025-07-29 19:17:37.064 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 64773
2025-07-29 19:17:37.090 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 64844
2025-07-29 19:20:55.263 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 70582
2025-07-29 19:20:55.265 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 70639
2025-07-29 19:24:11.755 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 71996
2025-07-29 19:26:17.475 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101118
2025-07-29 19:26:17.476 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 125200
2025-07-29 19:36:16.557 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 76122
2025-07-29 19:48:47.649 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 301095
2025-07-29 19:48:47.650 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 301094
2025-07-29 19:52:25.741 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 217540
2025-07-29 19:52:25.742 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 217524
2025-07-29 19:55:06.067 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 156559
2025-07-29 19:55:06.068 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 156557
2025-07-29 20:01:03.156 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 151006
2025-07-29 20:01:03.157 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 151007
2025-07-29 20:05:21.326 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 246476
2025-07-29 20:05:21.358 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 246524
2025-07-29 20:05:43.040 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-29 20:05:43.045 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-29 20:05:43.934 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-29 20:05:43.934 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-29 20:05:43.935 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-29 20:05:43.935 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-29 20:05:46.795 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-29 20:05:48.242 INFO [ main] com.my.college.App:61 - Started App in 6.195 seconds (JVM running for 7.388)
2025-07-29 20:05:48.253 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-29 20:05:53.450 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-29 20:05:53.450 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-29 20:05:53.450 ERROR [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-29 20:05:53.450 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-29 20:05:53.450 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-29 20:05:53.450 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-29 20:06:59.135 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 71988
2025-07-30 09:14:41.340 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-30 09:14:41.355 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-30 09:14:43.905 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-30 09:14:43.906 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-30 09:14:43.907 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-30 09:14:43.907 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-30 09:14:53.501 WARN [ main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:558 - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\com\my\college\auth\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysUserServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserMapper' defined in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\com\my\college\mybatis\mapper\SysUserMapper.class]: Invocation of init method failed; nested exception is java.lang.NoClassDefFoundError: com/my/college/mybatis/entity/SysUser$SysUserBuilder
2025-07-30 09:14:53.636 ERROR [ main] o.springframework.boot.SpringApplication:826 - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authController' defined in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\com\my\college\auth\controller\AuthController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysUserServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserMapper' defined in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\com\my\college\mybatis\mapper\SysUserMapper.class]: Invocation of init method failed; nested exception is java.lang.NoClassDefFoundError: com/my/college/mybatis/entity/SysUser$SysUserBuilder
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:798)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:228)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1358)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1204)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:879)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:878)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:550)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.my.college.App.main(App.java:40)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysUserServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserMapper' defined in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\com\my\college\mybatis\mapper\SysUserMapper.class]: Invocation of init method failed; nested exception is java.lang.NoClassDefFoundError: com/my/college/mybatis/entity/SysUser$SysUserBuilder
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:643)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:116)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:885)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:789)
	... 17 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sysUserMapper' defined in file [E:\git-project-fast\colledge-leader-address-book\java-api\target\classes\com\my\college\mybatis\mapper\SysUserMapper.class]: Invocation of init method failed; nested exception is java.lang.NoClassDefFoundError: com/my/college/mybatis/entity/SysUser$SysUserBuilder
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1796)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:595)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:222)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1287)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1207)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	... 31 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com/my/college/mybatis/entity/SysUser$SysUserBuilder
	at java.lang.Class.getDeclaredMethods0(Native Method)
	at java.lang.Class.privateGetDeclaredMethods(Unknown Source)
	at java.lang.Class.getDeclaredMethods(Unknown Source)
	at org.apache.ibatis.reflection.Reflector.getClassMethods(Reflector.java:280)
	at org.apache.ibatis.reflection.Reflector.addGetMethods(Reflector.java:88)
	at org.apache.ibatis.reflection.Reflector.<init>(Reflector.java:67)
	at java.util.concurrent.ConcurrentHashMap.computeIfAbsent(Unknown Source)
	at org.apache.ibatis.util.MapUtil.computeIfAbsent(MapUtil.java:36)
	at org.apache.ibatis.reflection.DefaultReflectorFactory.findForClass(DefaultReflectorFactory.java:44)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableFields(TableInfoHelper.java:277)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableInfo(TableInfoHelper.java:168)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableInfo(TableInfoHelper.java:144)
	at com.baomidou.mybatisplus.core.injector.AbstractSqlInjector.inspectInject(AbstractSqlInjector.java:50)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parserInjector(MybatisMapperAnnotationBuilder.java:131)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:121)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:83)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:119)
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
	at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1855)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1792)
	... 41 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.my.college.mybatis.entity.SysUser$SysUserBuilder
	at java.net.URLClassLoader.findClass(Unknown Source)
	at java.lang.ClassLoader.loadClass(Unknown Source)
	at sun.misc.Launcher$AppClassLoader.loadClass(Unknown Source)
	at java.lang.ClassLoader.loadClass(Unknown Source)
	... 62 common frames omitted
2025-07-30 09:18:10.964 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-30 09:18:10.969 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-30 09:18:12.192 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-30 09:18:12.193 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-30 09:18:12.193 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-30 09:18:12.193 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-30 09:18:15.288 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 09:18:17.013 INFO [ main] com.my.college.App:61 - Started App in 7.195 seconds (JVM running for 15.49)
2025-07-30 09:18:17.039 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-30 09:18:21.004 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 09:18:21.004 ERROR [http-nio-20000-exec-9] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 09:18:21.004 ERROR [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 09:18:21.004 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 09:18:21.004 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 09:18:21.004 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 09:18:38.444 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-30 09:18:38.450 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-30 09:18:39.421 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-30 09:18:39.422 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-30 09:18:39.422 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-30 09:18:39.422 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-30 09:18:42.301 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 09:18:43.692 INFO [ main] com.my.college.App:61 - Started App in 6.16 seconds (JVM running for 7.449)
2025-07-30 09:18:43.703 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-30 09:18:46.648 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 09:18:46.648 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 09:18:46.648 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 09:18:46.648 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 09:18:46.648 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 09:18:46.648 ERROR [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 09:37:31.325 WARN [RMI TCP Connection(4)-127.0.0.1] o.a.c.loader.WebappClassLoaderBase:173 - The web application [ROOT] appears to have started a thread named [Thread-4] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.lang.Thread.sleep(Native Method)
 cn.dev33.satoken.dao.SaTokenDaoDefaultImpl.lambda$initRefreshThread$0(SaTokenDaoDefaultImpl.java:247)
 cn.dev33.satoken.dao.SaTokenDaoDefaultImpl$$Lambda$681/2058520289.run(Unknown Source)
 java.lang.Thread.run(Unknown Source)
2025-07-30 09:37:37.420 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-30 09:37:37.425 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-30 09:37:38.284 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-30 09:37:38.284 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-30 09:37:38.285 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-30 09:37:38.285 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-30 09:37:41.071 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 09:37:42.450 INFO [ main] com.my.college.App:61 - Started App in 5.921 seconds (JVM running for 7.078)
2025-07-30 09:37:42.460 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-30 09:37:45.327 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 09:37:45.327 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 09:37:45.327 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 09:37:45.327 ERROR [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 09:37:45.327 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 09:37:45.327 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 09:58:21.744 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-30 09:58:21.751 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-30 09:58:22.673 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-30 09:58:22.674 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-30 09:58:22.674 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-30 09:58:22.674 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-30 09:58:25.634 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下职位:\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 intext:(\"faculty-led program manager\" OR \"Global Programs Manager\" OR \"International Student Advisor\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 09:58:27.027 INFO [ main] com.my.college.App:61 - Started App in 6.298 seconds (JVM running for 7.545)
2025-07-30 09:58:27.040 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-30 09:58:32.677 ERROR [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 09:58:32.677 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 09:58:32.677 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 09:58:32.677 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 09:58:32.677 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 09:58:32.677 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 09:58:36.393 ERROR [http-nio-20000-exec-9] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 09:58:36.393 ERROR [http-nio-20000-exec-8] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 09:58:36.400 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 09:58:36.400 ERROR [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 09:58:36.402 ERROR [http-nio-20000-exec-10] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 09:58:36.404 ERROR [http-nio-20000-exec-9] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 10:31:02.086 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 266558
2025-07-30 10:31:02.088 ERROR [http-nio-20000-exec-5] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 266623
2025-07-30 10:34:07.118 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 184504
2025-07-30 10:34:07.119 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 184603
2025-07-30 11:08:16.436 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101314
2025-07-30 11:08:16.436 ERROR [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 101239
2025-07-30 11:09:32.238 ERROR [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 75428
2025-07-30 11:09:32.243 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 75329
2025-07-30 11:30:55.624 ERROR [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 282331
2025-07-30 11:30:55.624 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 282332
2025-07-30 11:33:47.844 ERROR [http-nio-20000-exec-7] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 148147
2025-07-30 11:33:47.857 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 148142
2025-07-30 12:16:10.483 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 165383
2025-07-30 12:16:10.509 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 165409
2025-07-30 12:40:09.435 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 82539
2025-07-30 12:40:09.436 ERROR [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 82538
2025-07-30 12:41:00.142 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiModel = Qwen/Qwen3-14B
2025-07-30 12:41:00.224 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiSystemPrompt = -
2025-07-30 12:41:00.311 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiUserPrompt = Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:"Global Experience Office" OR "International Programs Center" OR "Office of International Education" OR "Study Abroad" 仅抓取以下职位:"Associate Director" OR "Director" OR "Faculty-led program director"</user_request><schema_block>{  "properties": {    "contactName": {       "description": "The contactName of the entity.",      "title": "contactName",      "type": "string" },    "department": {      "description": "The department of the entity.",      "title": "department",      "type": "string"    },    "position": {      "description": "The position of the  entity.",      "title": "position",      "type": "string"    },    "email": {      "description": "The email of the entity.",      "title": "email",      "type": "string"    },    "phone": {      "description": "The phone of the entity.",      "title": "phone",      "type": "string"    }  },  "required": [    "contactName" ],  "title": "BusinessData",  "type": "object"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using "//" or  "#" in the JSON output. - Do NOT using "```json" and "```" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.
2025-07-30 12:41:00.398 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiApiKey = sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud
2025-07-30 12:41:00.477 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiTimeout = 300
2025-07-30 12:41:00.566 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: markdownTimeout = 10
2025-07-30 12:41:00.651 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleApiKey = AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc
2025-07-30 12:41:00.734 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleEngineID = 66818a77d1c67403b
2025-07-30 12:41:00.823 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleKeyword = site:学校网址 (intitle:"contact us" OR "directory" OR "staff") (intext:"Global Experience Office" OR "International Programs Center" OR "Office of International Education" OR "Study Abroad") (intext:"@学校网址" OR intext:"email" OR intext:"contact") -inurl:("archive" | "old" | "pdf") -filetype:pdf -filetype:xlsx -filetype:doc
2025-07-30 12:41:00.823 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:73 - 系统参数批量更新完成
2025-07-30 12:41:00.857 INFO [http-nio-20000-exec-2] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:\"Global Experience Office\" OR \"International Programs Center\" OR \"Office of International Education\" OR \"Study Abroad\" 仅抓取以下职位:\"Associate Director\" OR \"Director\" OR \"Faculty-led program director\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 (intitle:\"contact us\" OR \"directory\" OR \"staff\") (intext:\"Global Experience Office\" OR \"International Programs Center\" OR \"Office of International Education\" OR \"Study Abroad\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 12:41:01.015 INFO [http-nio-20000-exec-5] c.d.forest.config.ForestConfiguration:22 - [Forest] Http Backend: okhttp3
2025-07-30 12:41:07.178 ERROR [http-nio-20000-exec-5] c.m.c.f.g.onerror.CustomSearchOnError:38 - CustomSearchErrCode：网络异常, detail:java.net.SocketTimeoutException: connect timed out
2025-07-30 12:41:12.188 ERROR [http-nio-20000-exec-5] c.m.c.f.g.onerror.CustomSearchOnError:38 - CustomSearchErrCode：网络异常, detail:java.net.SocketTimeoutException: connect timed out
2025-07-30 12:58:43.684 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-30 12:58:43.688 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-30 12:58:44.452 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-30 12:58:44.452 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-30 12:58:44.453 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-30 12:58:44.453 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-30 12:58:46.901 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:\"Global Experience Office\" OR \"International Programs Center\" OR \"Office of International Education\" OR \"Study Abroad\" 仅抓取以下职位:\"Associate Director\" OR \"Director\" OR \"Faculty-led program director\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 (intitle:\"contact us\" OR \"directory\" OR \"staff\") (intext:\"Global Experience Office\" OR \"International Programs Center\" OR \"Office of International Education\" OR \"Study Abroad\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 12:58:48.059 INFO [ main] com.my.college.App:61 - Started App in 5.252 seconds (JVM running for 6.363)
2025-07-30 12:58:48.069 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-30 12:58:52.113 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 12:58:52.113 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 12:58:52.113 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 12:58:52.113 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 12:58:52.113 ERROR [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 12:58:52.113 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 12:59:07.840 INFO [http-nio-20000-exec-6] c.d.forest.config.ForestConfiguration:22 - [Forest] Http Backend: okhttp3
2025-07-30 12:59:13.792 ERROR [http-nio-20000-exec-6] c.m.c.f.g.onerror.CustomSearchOnError:38 - CustomSearchErrCode：网络异常, detail:java.net.SocketTimeoutException: connect timed out
2025-07-30 12:59:18.800 ERROR [http-nio-20000-exec-6] c.m.c.f.g.onerror.CustomSearchOnError:38 - CustomSearchErrCode：网络异常, detail:java.net.SocketTimeoutException: connect timed out
2025-07-30 13:03:31.253 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-30 13:03:31.259 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-30 13:03:32.208 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-30 13:03:32.208 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-30 13:03:32.208 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-30 13:03:32.209 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-30 13:03:35.145 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:\"Global Experience Office\" OR \"International Programs Center\" OR \"Office of International Education\" OR \"Study Abroad\" 仅抓取以下职位:\"Associate Director\" OR \"Director\" OR \"Faculty-led program director\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 (intitle:\"contact us\" OR \"directory\" OR \"staff\") (intext:\"Global Experience Office\" OR \"International Programs Center\" OR \"Office of International Education\" OR \"Study Abroad\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 13:03:36.493 INFO [ main] com.my.college.App:61 - Started App in 6.516 seconds (JVM running for 7.882)
2025-07-30 13:03:36.505 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-30 13:03:40.962 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 13:03:40.962 ERROR [http-nio-20000-exec-1] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 13:03:40.962 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 13:03:40.962 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 13:03:40.962 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 13:03:40.962 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 13:42:14.392 INFO [ main] com.my.college.App:72 - Tomcat Port 20000 is available. Starting application >> 
2025-07-30 13:42:14.398 INFO [ main] com.my.college.App:655 - The following profiles are active: aliyun,aliyun-sa,aliyun-db,aliyun-forest
2025-07-30 13:42:15.310 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'deepSeekClient' and Proxy of 'com.my.college.forest.deepseek.DeepSeekClient' client interface
2025-07-30 13:42:15.310 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'googleClient' and Proxy of 'com.my.college.forest.google.GoogleClient' client interface
2025-07-30 13:42:15.311 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'markdownClient' and Proxy of 'com.my.college.forest.markdown.MarkdownClient' client interface
2025-07-30 13:42:15.311 INFO [ main] c.d.f.scanner.ClassPathClientScanner:153 - [Forest] Created Forest Client Bean with name 'siliconflowClient' and Proxy of 'com.my.college.forest.siliconflow.SiliconflowClient' client interface
2025-07-30 13:42:18.207 INFO [ main] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:\"Global Experience Office\" OR \"International Programs Center\" OR \"Office of International Education\" OR \"Study Abroad\" 仅抓取以下职位:\"Associate Director\" OR \"Director\" OR \"Faculty-led program director\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 (intitle:\"contact us\" OR \"directory\" OR \"staff\") (intext:\"Global Experience Office\" OR \"International Programs Center\" OR \"Office of International Education\" OR \"Study Abroad\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 13:42:19.710 INFO [ main] com.my.college.App:61 - Started App in 6.338 seconds (JVM running for 7.536)
2025-07-30 13:42:19.723 INFO [ main] com.my.college.App:41 -  >> Web Server Started on port: 20000
2025-07-30 13:42:24.062 ERROR [http-nio-20000-exec-5] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/dept/list, Err-detail:接口401
2025-07-30 13:42:24.062 ERROR [http-nio-20000-exec-6] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/title/list, Err-detail:接口401
2025-07-30 13:42:24.062 ERROR [http-nio-20000-exec-2] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/detail, Err-detail:接口401
2025-07-30 13:42:24.062 ERROR [http-nio-20000-exec-7] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/position/list, Err-detail:接口401
2025-07-30 13:42:24.062 ERROR [http-nio-20000-exec-4] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/school/list, Err-detail:接口401
2025-07-30 13:42:24.062 ERROR [http-nio-20000-exec-3] com.my.college.exception.ExceptionAspect:52 - Err-uri:http://localhost:20000/api/sys-param/markdown-server-url, Err-detail:接口401
2025-07-30 13:43:07.411 INFO [http-nio-20000-exec-5] c.d.forest.config.ForestConfiguration:22 - [Forest] Http Backend: okhttp3
2025-07-30 13:44:32.705 ERROR [http-nio-20000-exec-10] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 120627
2025-07-30 13:44:32.705 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 120627
2025-07-30 13:45:52.641 ERROR [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 67452
2025-07-30 13:45:52.706 ERROR [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 68748
2025-07-30 13:46:15.408 INFO [http-nio-20000-exec-7] com.my.college.task.TaskEntrance:87 - 系统参数检测结果: 通过
2025-07-30 13:46:16.567 INFO [http-nio-20000-exec-7] c.m.c.service.impl.TaskLogServiceImpl:40 - [70/1950432737093607424/14586] 任务创建成功
2025-07-30 13:46:16.733 INFO [http-nio-20000-exec-7] c.m.c.service.impl.TaskLogServiceImpl:40 - [70/1950432737093607424/14586] 请求参数准备完毕
2025-07-30 13:46:16.867 INFO [http-nio-20000-exec-7] c.m.c.service.impl.TaskLogServiceImpl:40 - [70/1950432738494504960/14587] 任务创建成功
2025-07-30 13:46:17.013 INFO [http-nio-20000-exec-7] c.m.c.service.impl.TaskLogServiceImpl:40 - [70/1950432738494504960/14587] 请求参数准备完毕
2025-07-30 13:46:17.354 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.TaskLogServiceImpl:40 - [70/1950432737093607424/14586] 接口调用成功, 等待响应
2025-07-30 13:46:17.512 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.TaskLogServiceImpl:40 - [70/1950432737093607424/14586] 响应成功
2025-07-30 13:46:17.657 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432741774450688] 子任务创建成功
2025-07-30 13:46:17.787 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432741774450688] markdown请求参数准备完毕
2025-07-30 13:46:17.917 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.TaskLogServiceImpl:40 - [70/1950432738494504960/14587] 接口调用成功, 等待响应
2025-07-30 13:46:17.917 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432742885941248] 子任务创建成功
2025-07-30 13:46:18.058 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.TaskLogServiceImpl:40 - [70/1950432738494504960/14587] 响应成功
2025-07-30 13:46:18.064 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432742885941248] markdown请求参数准备完毕
2025-07-30 13:46:18.195 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432744043569152] 子任务创建成功
2025-07-30 13:46:18.201 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432744068734976] 子任务创建成功
2025-07-30 13:46:18.327 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432744043569152] markdown请求参数准备完毕
2025-07-30 13:46:18.363 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432744068734976] markdown请求参数准备完毕
2025-07-30 13:46:18.467 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432745201197056] 子任务创建成功
2025-07-30 13:46:18.507 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432745310248960] 子任务创建成功
2025-07-30 13:46:18.591 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432745201197056] markdown请求参数准备完毕
2025-07-30 13:46:18.640 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432745310248960] markdown请求参数准备完毕
2025-07-30 13:46:18.713 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432746241384448] 子任务创建成功
2025-07-30 13:46:18.780 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432746526597120] 子任务创建成功
2025-07-30 13:46:18.836 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432746241384448] markdown请求参数准备完毕
2025-07-30 13:46:18.912 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432746526597120] markdown请求参数准备完毕
2025-07-30 13:46:18.958 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432747277377536] 子任务创建成功
2025-07-30 13:46:19.046 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432747650670592] 子任务创建成功
2025-07-30 13:46:19.080 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432747277377536] markdown请求参数准备完毕
2025-07-30 13:46:19.189 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432747650670592] markdown请求参数准备完毕
2025-07-30 13:46:19.204 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432748300787712] 子任务创建成功
2025-07-30 13:46:19.328 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432748770549760] 子任务创建成功
2025-07-30 13:46:19.333 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432748300787712] markdown请求参数准备完毕
2025-07-30 13:46:19.456 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432749374529536] 子任务创建成功
2025-07-30 13:46:19.457 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432748770549760] markdown请求参数准备完毕
2025-07-30 13:46:19.598 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432749374529536] markdown请求参数准备完毕
2025-07-30 13:46:19.598 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432749940760576] 子任务创建成功
2025-07-30 13:46:19.731 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432750515380224] 子任务创建成功
2025-07-30 13:46:19.738 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432749940760576] markdown请求参数准备完毕
2025-07-30 13:46:19.859 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432750515380224] markdown请求参数准备完毕
2025-07-30 13:46:19.864 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432751077416960] 子任务创建成功
2025-07-30 13:46:20.004 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432751652036608] 子任务创建成功
2025-07-30 13:46:20.013 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432751077416960] markdown请求参数准备完毕
2025-07-30 13:46:20.133 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432751652036608] markdown请求参数准备完毕
2025-07-30 13:46:20.142 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432752214073344] 子任务创建成功
2025-07-30 13:46:20.257 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432752729972736] 子任务创建成功
2025-07-30 13:46:20.269 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432752214073344] markdown请求参数准备完毕
2025-07-30 13:46:20.377 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432752729972736] markdown请求参数准备完毕
2025-07-30 13:46:20.521 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432753786937344] 子任务创建成功
2025-07-30 13:46:20.645 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432753786937344] markdown请求参数准备完毕
2025-07-30 13:46:35.748 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432741774450688] 接口调用成功, 等待响应
2025-07-30 13:46:35.762 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432742885941248] 接口调用成功, 等待响应
2025-07-30 13:46:35.882 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432741774450688] markdown响应成功
2025-07-30 13:46:35.913 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432742885941248] markdown响应成功
2025-07-30 13:46:36.053 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432741774450688] AI请求参数准备完毕
2025-07-30 13:46:36.092 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432742885941248] AI请求参数准备完毕
2025-07-30 13:46:37.707 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432744043569152] 接口调用成功, 等待响应
2025-07-30 13:46:37.848 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432744043569152] markdown响应成功
2025-07-30 13:46:37.988 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432744043569152] AI请求参数准备完毕
2025-07-30 13:47:10.977 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432744068734976] 接口调用成功, 等待响应
2025-07-30 13:47:11.105 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432744068734976] markdown响应成功
2025-07-30 13:47:11.239 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432744068734976] AI请求参数准备完毕
2025-07-30 13:47:20.367 ERROR [ForkJoinPool.commonPool-worker-1] c.m.c.f.markdown.onerror.MarkdownOnError:26 - Markdown接口异常：java.net.SocketTimeoutException: timeout
2025-07-30 13:47:20.367 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432746241384448] 接口调用成功, 等待响应
2025-07-30 13:47:20.490 ERROR [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:47 - [1950432738494504960/1950432746241384448] markdown接口调用失败: com.my.college.exception.BusinessException: java.net.SocketTimeoutException: timeout
2025-07-30 13:47:20.730 ERROR [ForkJoinPool.commonPool-worker-2] c.m.c.f.markdown.onerror.MarkdownOnError:26 - Markdown接口异常：java.net.SocketTimeoutException: timeout
2025-07-30 13:47:20.731 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432745310248960] 接口调用成功, 等待响应
2025-07-30 13:47:20.863 ERROR [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:47 - [1950432737093607424/1950432745310248960] markdown接口调用失败: com.my.college.exception.BusinessException: java.net.SocketTimeoutException: timeout
2025-07-30 13:47:31.918 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432745201197056] 接口调用成功, 等待响应
2025-07-30 13:47:32.052 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432745201197056] markdown响应成功
2025-07-30 13:47:32.200 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432745201197056] AI请求参数准备完毕
2025-07-30 13:47:37.018 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432747277377536] 接口调用成功, 等待响应
2025-07-30 13:47:37.019 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432746526597120] 接口调用成功, 等待响应
2025-07-30 13:47:37.067 ERROR [ForkJoinPool.commonPool-worker-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 60951
2025-07-30 13:47:37.261 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432747277377536] markdown响应成功
2025-07-30 13:47:37.402 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432746526597120] markdown响应成功
2025-07-30 13:47:37.559 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432747277377536] AI请求参数准备完毕
2025-07-30 13:47:37.669 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432746526597120] AI请求参数准备完毕
2025-07-30 13:47:38.086 ERROR [ForkJoinPool.commonPool-worker-5] c.m.c.f.markdown.onerror.MarkdownOnError:26 - Markdown接口异常：java.net.SocketTimeoutException: timeout
2025-07-30 13:47:38.087 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432748300787712] 接口调用成功, 等待响应
2025-07-30 13:47:38.201 ERROR [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:47 - [1950432738494504960/1950432748300787712] markdown接口调用失败: com.my.college.exception.BusinessException: java.net.SocketTimeoutException: timeout
2025-07-30 13:47:46.152 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432744068734976] AI接口调用成功, 等待响应
2025-07-30 13:47:46.287 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432744068734976] AI响应成功
2025-07-30 13:47:46.465 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432744068734976] AI结果保存成功
2025-07-30 13:47:52.935 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432747650670592] 接口调用成功, 等待响应
2025-07-30 13:47:53.090 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432747650670592] markdown响应成功
2025-07-30 13:47:53.247 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432747650670592] AI请求参数准备完毕
2025-07-30 13:47:54.078 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432749374529536] 接口调用成功, 等待响应
2025-07-30 13:47:54.218 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432749374529536] markdown响应成功
2025-07-30 13:47:54.386 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432749374529536] AI请求参数准备完毕
2025-07-30 13:47:55.681 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432750515380224] 接口调用成功, 等待响应
2025-07-30 13:47:55.810 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432750515380224] markdown响应成功
2025-07-30 13:47:55.932 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432750515380224] AI请求参数准备完毕
2025-07-30 13:47:57.510 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432744043569152] AI接口调用成功, 等待响应
2025-07-30 13:47:57.636 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432744043569152] AI响应成功
2025-07-30 13:47:57.731 WARN [ForkJoinPool.commonPool-worker-5] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41fa4bab] Transaction not enabled
2025-07-30 13:47:57.860 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432744043569152] AI结果保存成功
2025-07-30 13:47:58.123 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432751652036608] 接口调用成功, 等待响应
2025-07-30 13:47:58.243 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432751652036608] markdown响应成功
2025-07-30 13:47:58.373 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432751652036608] AI请求参数准备完毕
2025-07-30 13:48:01.526 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432750515380224] AI接口调用成功, 等待响应
2025-07-30 13:48:01.651 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432750515380224] AI响应成功
2025-07-30 13:48:01.831 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432750515380224] AI结果保存成功
2025-07-30 13:48:03.974 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432742885941248] AI接口调用成功, 等待响应
2025-07-30 13:48:04.105 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432742885941248] AI响应成功
2025-07-30 13:48:04.281 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432742885941248] AI结果保存成功
2025-07-30 13:48:05.837 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432745201197056] AI接口调用成功, 等待响应
2025-07-30 13:48:05.962 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432745201197056] AI响应成功
2025-07-30 13:48:06.053 WARN [ForkJoinPool.commonPool-worker-6] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cab8e6e] Transaction not enabled
2025-07-30 13:48:06.196 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432745201197056] AI结果保存成功
2025-07-30 13:48:13.643 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432741774450688] AI接口调用成功, 等待响应
2025-07-30 13:48:13.777 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432741774450688] AI响应成功
2025-07-30 13:48:13.951 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432741774450688] AI结果保存成功
2025-07-30 13:48:14.601 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432751652036608] AI接口调用成功, 等待响应
2025-07-30 13:48:14.729 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432751652036608] AI响应成功
2025-07-30 13:48:14.902 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432751652036608] AI结果保存成功
2025-07-30 13:48:21.564 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432746526597120] AI接口调用成功, 等待响应
2025-07-30 13:48:21.692 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432746526597120] AI响应成功
2025-07-30 13:48:21.856 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432746526597120] AI结果保存成功
2025-07-30 13:48:28.132 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432747650670592] AI接口调用成功, 等待响应
2025-07-30 13:48:28.254 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432747650670592] AI响应成功
2025-07-30 13:48:28.351 WARN [ForkJoinPool.commonPool-worker-6] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5d14f913] Transaction not enabled
2025-07-30 13:48:28.501 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432747650670592] AI结果保存成功
2025-07-30 13:48:33.683 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432752729972736] 接口调用成功, 等待响应
2025-07-30 13:48:33.684 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432748770549760] 接口调用成功, 等待响应
2025-07-30 13:48:33.796 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432748770549760] markdown响应成功
2025-07-30 13:48:33.806 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432752729972736] markdown响应成功
2025-07-30 13:48:33.946 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432752729972736] AI请求参数准备完毕
2025-07-30 13:48:33.946 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432748770549760] AI请求参数准备完毕
2025-07-30 13:48:34.769 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432753786937344] 接口调用成功, 等待响应
2025-07-30 13:48:34.900 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432753786937344] markdown响应成功
2025-07-30 13:48:35.043 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432753786937344] AI请求参数准备完毕
2025-07-30 13:48:40.661 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432749374529536] AI接口调用成功, 等待响应
2025-07-30 13:48:41.049 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432749374529536] AI响应成功
2025-07-30 13:48:41.245 WARN [ForkJoinPool.commonPool-worker-6] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@665efa80] Transaction not enabled
2025-07-30 13:48:41.379 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432749374529536] AI结果保存成功
2025-07-30 13:48:41.442 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432747277377536] AI接口调用成功, 等待响应
2025-07-30 13:48:41.565 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432747277377536] AI响应成功
2025-07-30 13:48:41.711 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432747277377536] AI结果保存成功
2025-07-30 13:48:43.081 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432752729972736] AI接口调用成功, 等待响应
2025-07-30 13:48:43.236 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432752729972736] AI响应成功
2025-07-30 13:48:43.400 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432752729972736] AI结果保存成功
2025-07-30 13:48:45.763 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432753786937344] AI接口调用成功, 等待响应
2025-07-30 13:48:45.884 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432753786937344] AI响应成功
2025-07-30 13:48:46.064 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432738494504960/1950432753786937344] AI结果保存成功
2025-07-30 13:48:49.280 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432751077416960] 接口调用成功, 等待响应
2025-07-30 13:48:49.284 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432749940760576] 接口调用成功, 等待响应
2025-07-30 13:48:49.448 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432749940760576] markdown响应成功
2025-07-30 13:48:49.456 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432751077416960] markdown响应成功
2025-07-30 13:48:49.586 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432749940760576] AI请求参数准备完毕
2025-07-30 13:48:49.664 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432751077416960] AI请求参数准备完毕
2025-07-30 13:48:54.495 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432748770549760] AI接口调用成功, 等待响应
2025-07-30 13:48:54.637 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432748770549760] AI响应成功
2025-07-30 13:48:54.836 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432748770549760] AI结果保存成功
2025-07-30 13:48:55.789 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432752214073344] 接口调用成功, 等待响应
2025-07-30 13:48:55.936 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432752214073344] markdown响应成功
2025-07-30 13:48:56.075 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432752214073344] AI请求参数准备完毕
2025-07-30 13:49:01.811 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432749940760576] AI接口调用成功, 等待响应
2025-07-30 13:49:01.935 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432749940760576] AI响应成功
2025-07-30 13:49:02.035 WARN [ForkJoinPool.commonPool-worker-2] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@187b470e] Transaction not enabled
2025-07-30 13:49:02.171 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432749940760576] AI结果保存成功
2025-07-30 13:49:08.768 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432751077416960] AI接口调用成功, 等待响应
2025-07-30 13:49:08.899 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432751077416960] AI响应成功
2025-07-30 13:49:09.071 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432751077416960] AI结果保存成功
2025-07-30 13:49:24.259 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432752214073344] AI接口调用成功, 等待响应
2025-07-30 13:49:24.392 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432752214073344] AI响应成功
2025-07-30 13:49:24.569 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950432737093607424/1950432752214073344] AI结果保存成功
2025-07-30 13:50:48.033 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 83336
2025-07-30 13:50:48.059 ERROR [http-nio-20000-exec-8] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 118389
2025-07-30 13:56:07.642 ERROR [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 276584
2025-07-30 13:56:07.671 ERROR [http-nio-20000-exec-4] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 276659
2025-07-30 13:58:55.446 ERROR [http-nio-20000-exec-9] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 124955
2025-07-30 13:58:55.446 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 124993
2025-07-30 14:00:41.087 ERROR [http-nio-20000-exec-3] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 105173
2025-07-30 14:03:00.121 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 86669
2025-07-30 14:03:00.150 ERROR [http-nio-20000-exec-2] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 91645
2025-07-30 14:24:26.799 INFO [http-nio-20000-exec-5] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiModel = Qwen/Qwen3-14B
2025-07-30 14:24:27.280 INFO [http-nio-20000-exec-5] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiSystemPrompt = -
2025-07-30 14:24:27.386 INFO [http-nio-20000-exec-5] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiUserPrompt = Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:"Global Experience Office" OR "International Programs Center" OR "Study Abroad" OR "Office of International Education" 仅抓取以下职位:"Associate Director" OR "Director" OR "Faculty-led program director"</user_request><schema_block>{  "properties": {    "contactName": {       "description": "The contactName of the entity.",      "title": "contactName",      "type": "string" },    "department": {      "description": "The department of the entity.",      "title": "department",      "type": "string"    },    "position": {      "description": "The position of the  entity.",      "title": "position",      "type": "string"    },    "email": {      "description": "The email of the entity.",      "title": "email",      "type": "string"    },    "phone": {      "description": "The phone of the entity.",      "title": "phone",      "type": "string"    }  },  "required": [    "contactName" ],  "title": "BusinessData",  "type": "object"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using "//" or  "#" in the JSON output. - Do NOT using "```json" and "```" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.
2025-07-30 14:24:27.483 INFO [http-nio-20000-exec-5] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiApiKey = sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud
2025-07-30 14:24:27.581 INFO [http-nio-20000-exec-5] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiTimeout = 300
2025-07-30 14:24:27.680 INFO [http-nio-20000-exec-5] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: markdownTimeout = 10
2025-07-30 14:24:27.782 INFO [http-nio-20000-exec-5] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleApiKey = AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc
2025-07-30 14:24:27.883 INFO [http-nio-20000-exec-5] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleEngineID = 66818a77d1c67403b
2025-07-30 14:24:27.987 INFO [http-nio-20000-exec-5] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleKeyword = site:学校网址 (intitle:"contact us" OR "directory" OR "staff") (intext:"Global Experience Office" OR "International Programs Center" OR "Study Abroad" OR "Office of International Education") (intext:"@学校网址" OR intext:"email" OR intext:"contact") -inurl:("archive" | "old" | "pdf") -filetype:pdf -filetype:xlsx -filetype:doc
2025-07-30 14:24:27.988 INFO [http-nio-20000-exec-5] c.m.c.service.impl.SysParamServiceImpl:73 - 系统参数批量更新完成
2025-07-30 14:24:28.033 INFO [http-nio-20000-exec-5] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:\"Global Experience Office\" OR \"International Programs Center\" OR \"Study Abroad\" OR \"Office of International Education\" 仅抓取以下职位:\"Associate Director\" OR \"Director\" OR \"Faculty-led program director\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 (intitle:\"contact us\" OR \"directory\" OR \"staff\") (intext:\"Global Experience Office\" OR \"International Programs Center\" OR \"Study Abroad\" OR \"Office of International Education\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 14:24:50.418 INFO [http-nio-20000-exec-3] com.my.college.task.TaskEntrance:87 - 系统参数检测结果: 通过
2025-07-30 14:24:50.725 INFO [http-nio-20000-exec-3] c.m.c.service.impl.TaskLogServiceImpl:40 - [71/1950442443514175488/14583] 任务创建成功
2025-07-30 14:24:50.865 INFO [http-nio-20000-exec-3] c.m.c.service.impl.TaskLogServiceImpl:40 - [71/1950442443514175488/14583] 请求参数准备完毕
2025-07-30 14:24:51.445 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.TaskLogServiceImpl:40 - [71/1950442443514175488/14583] 接口调用成功, 等待响应
2025-07-30 14:24:51.580 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.TaskLogServiceImpl:40 - [71/1950442443514175488/14583] 响应成功
2025-07-30 14:24:51.691 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442447607816192] 子任务创建成功
2025-07-30 14:24:51.814 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442447607816192] markdown请求参数准备完毕
2025-07-30 14:24:51.947 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442448664780800] 子任务创建成功
2025-07-30 14:24:52.062 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442448664780800] markdown请求参数准备完毕
2025-07-30 14:24:52.193 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442449658830848] 子任务创建成功
2025-07-30 14:24:52.326 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442449658830848] markdown请求参数准备完毕
2025-07-30 14:24:52.462 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442450740961280] 子任务创建成功
2025-07-30 14:24:52.603 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442450740961280] markdown请求参数准备完毕
2025-07-30 14:24:52.713 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442451898589184] 子任务创建成功
2025-07-30 14:24:52.825 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442451898589184] markdown请求参数准备完毕
2025-07-30 14:24:52.941 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442452833918976] 子任务创建成功
2025-07-30 14:24:53.058 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442452833918976] markdown请求参数准备完毕
2025-07-30 14:24:53.183 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442453857329152] 子任务创建成功
2025-07-30 14:24:53.323 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442453857329152] markdown请求参数准备完毕
2025-07-30 14:24:53.435 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442454918488064] 子任务创建成功
2025-07-30 14:24:53.567 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442454918488064] markdown请求参数准备完毕
2025-07-30 14:24:53.686 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442455962869760] 子任务创建成功
2025-07-30 14:24:53.797 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442455962869760] markdown请求参数准备完毕
2025-07-30 14:24:53.917 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442456948531200] 子任务创建成功
2025-07-30 14:24:54.065 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442456948531200] markdown请求参数准备完毕
2025-07-30 14:24:54.184 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442447607816192] 接口调用成功, 等待响应
2025-07-30 14:24:54.298 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442447607816192] markdown响应成功
2025-07-30 14:24:54.411 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442447607816192] AI请求参数准备完毕
2025-07-30 14:24:56.479 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442448664780800] 接口调用成功, 等待响应
2025-07-30 14:24:56.606 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442448664780800] markdown响应成功
2025-07-30 14:24:56.741 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442448664780800] AI请求参数准备完毕
2025-07-30 14:24:58.846 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442449658830848] 接口调用成功, 等待响应
2025-07-30 14:24:59.002 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442449658830848] markdown响应成功
2025-07-30 14:24:59.134 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442449658830848] AI请求参数准备完毕
2025-07-30 14:25:01.435 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442450740961280] 接口调用成功, 等待响应
2025-07-30 14:25:01.584 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442450740961280] markdown响应成功
2025-07-30 14:25:01.729 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442450740961280] AI请求参数准备完毕
2025-07-30 14:25:05.426 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442451898589184] 接口调用成功, 等待响应
2025-07-30 14:25:05.568 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442451898589184] markdown响应成功
2025-07-30 14:25:05.761 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442451898589184] AI请求参数准备完毕
2025-07-30 14:25:08.084 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442454918488064] 接口调用成功, 等待响应
2025-07-30 14:25:08.218 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442454918488064] markdown响应成功
2025-07-30 14:25:08.384 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442454918488064] AI请求参数准备完毕
2025-07-30 14:25:10.571 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442452833918976] 接口调用成功, 等待响应
2025-07-30 14:25:10.706 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442452833918976] markdown响应成功
2025-07-30 14:25:10.745 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442447607816192] AI接口调用成功, 等待响应
2025-07-30 14:25:10.845 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442452833918976] AI请求参数准备完毕
2025-07-30 14:25:10.880 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442447607816192] AI响应成功
2025-07-30 14:25:10.972 WARN [ForkJoinPool.commonPool-worker-1] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@36b1026b] Transaction not enabled
2025-07-30 14:25:11.095 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442447607816192] AI结果保存成功
2025-07-30 14:25:13.042 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442455962869760] 接口调用成功, 等待响应
2025-07-30 14:25:13.159 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442455962869760] markdown响应成功
2025-07-30 14:25:13.288 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442455962869760] AI请求参数准备完毕
2025-07-30 14:25:15.331 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442453857329152] 接口调用成功, 等待响应
2025-07-30 14:25:15.331 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442456948531200] 接口调用成功, 等待响应
2025-07-30 14:25:15.451 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442456948531200] markdown响应成功
2025-07-30 14:25:15.481 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442453857329152] markdown响应成功
2025-07-30 14:25:15.595 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442456948531200] AI请求参数准备完毕
2025-07-30 14:25:15.645 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442453857329152] AI请求参数准备完毕
2025-07-30 14:25:24.362 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442450740961280] AI接口调用成功, 等待响应
2025-07-30 14:25:24.498 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442450740961280] AI响应成功
2025-07-30 14:25:24.664 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442450740961280] AI结果保存成功
2025-07-30 14:25:25.940 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442449658830848] AI接口调用成功, 等待响应
2025-07-30 14:25:26.097 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442449658830848] AI响应成功
2025-07-30 14:25:26.295 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442449658830848] AI结果保存成功
2025-07-30 14:25:26.738 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442454918488064] AI接口调用成功, 等待响应
2025-07-30 14:25:26.873 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442454918488064] AI响应成功
2025-07-30 14:25:26.975 WARN [ForkJoinPool.commonPool-worker-6] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d33c335] Transaction not enabled
2025-07-30 14:25:27.114 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442454918488064] AI结果保存成功
2025-07-30 14:25:37.357 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442456948531200] AI接口调用成功, 等待响应
2025-07-30 14:25:37.519 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442456948531200] AI响应成功
2025-07-30 14:25:37.634 WARN [ForkJoinPool.commonPool-worker-4] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1427ae9] Transaction not enabled
2025-07-30 14:25:37.662 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442452833918976] AI接口调用成功, 等待响应
2025-07-30 14:25:37.804 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442456948531200] AI结果保存成功
2025-07-30 14:25:37.806 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442452833918976] AI响应成功
2025-07-30 14:25:37.901 WARN [ForkJoinPool.commonPool-worker-3] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ed7b310] Transaction not enabled
2025-07-30 14:25:38.031 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442452833918976] AI结果保存成功
2025-07-30 14:25:42.384 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442448664780800] AI接口调用成功, 等待响应
2025-07-30 14:25:42.558 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442448664780800] AI响应成功
2025-07-30 14:25:42.737 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442448664780800] AI结果保存成功
2025-07-30 14:25:44.318 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442453857329152] AI接口调用成功, 等待响应
2025-07-30 14:25:44.462 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442453857329152] AI响应成功
2025-07-30 14:25:44.552 WARN [ForkJoinPool.commonPool-worker-0] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@126bf91c] Transaction not enabled
2025-07-30 14:25:44.683 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442453857329152] AI结果保存成功
2025-07-30 14:25:48.045 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442455962869760] AI接口调用成功, 等待响应
2025-07-30 14:25:48.174 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442455962869760] AI响应成功
2025-07-30 14:25:48.274 WARN [ForkJoinPool.commonPool-worker-1] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77c4efad] Transaction not enabled
2025-07-30 14:25:48.413 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442455962869760] AI结果保存成功
2025-07-30 14:26:25.380 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442451898589184] AI接口调用成功, 等待响应
2025-07-30 14:26:25.525 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442451898589184] AI响应成功
2025-07-30 14:26:25.607 WARN [ForkJoinPool.commonPool-worker-2] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@40c0e571] Transaction not enabled
2025-07-30 14:26:25.729 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950442443514175488/1950442451898589184] AI结果保存成功
2025-07-30 14:26:25.869 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.TaskLogServiceImpl:40 - [71/1950442443514175488/14583] 主任务处理完毕
2025-07-30 14:26:48.273 ERROR [http-nio-20000-exec-6] c.a.druid.pool.DruidAbstractDataSource:1481 - discard long time none received connection. , jdbcUrl : *************************************************************************************************************************************************************************************************, jdbcUrl : *************************************************************************************************************************************************************************************************, lastPacketReceivedIdleMillis : 70342
2025-07-30 14:26:59.914 INFO [http-nio-20000-exec-3] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiModel = Qwen/Qwen3-14B
2025-07-30 14:27:00.000 INFO [http-nio-20000-exec-3] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiSystemPrompt = -
2025-07-30 14:27:00.092 INFO [http-nio-20000-exec-3] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiUserPrompt = Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:"Study Abroad" OR "Office of International Education" 仅抓取以下职位:"Associate Director" OR "Director"</user_request><schema_block>{  "properties": {    "contactName": {       "description": "The contactName of the entity.",      "title": "contactName",      "type": "string" },    "department": {      "description": "The department of the entity.",      "title": "department",      "type": "string"    },    "position": {      "description": "The position of the  entity.",      "title": "position",      "type": "string"    },    "email": {      "description": "The email of the entity.",      "title": "email",      "type": "string"    },    "phone": {      "description": "The phone of the entity.",      "title": "phone",      "type": "string"    }  },  "required": [    "contactName" ],  "title": "BusinessData",  "type": "object"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using "//" or  "#" in the JSON output. - Do NOT using "```json" and "```" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.
2025-07-30 14:27:00.194 INFO [http-nio-20000-exec-3] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiApiKey = sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud
2025-07-30 14:27:00.301 INFO [http-nio-20000-exec-3] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiTimeout = 300
2025-07-30 14:27:00.402 INFO [http-nio-20000-exec-3] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: markdownTimeout = 10
2025-07-30 14:27:00.501 INFO [http-nio-20000-exec-3] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleApiKey = AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc
2025-07-30 14:27:00.598 INFO [http-nio-20000-exec-3] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleEngineID = 66818a77d1c67403b
2025-07-30 14:27:00.686 INFO [http-nio-20000-exec-3] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleKeyword = site:学校网址 (intitle:"contact us" OR "directory" OR "staff") (intext:"Study Abroad" OR "Office of International Education") (intext:"@学校网址" OR intext:"email" OR intext:"contact") -inurl:("archive" | "old" | "pdf") -filetype:pdf -filetype:xlsx -filetype:doc
2025-07-30 14:27:00.687 INFO [http-nio-20000-exec-3] c.m.c.service.impl.SysParamServiceImpl:73 - 系统参数批量更新完成
2025-07-30 14:27:00.719 INFO [http-nio-20000-exec-3] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:\"Study Abroad\" OR \"Office of International Education\" 仅抓取以下职位:\"Associate Director\" OR \"Director\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 (intitle:\"contact us\" OR \"directory\" OR \"staff\") (intext:\"Study Abroad\" OR \"Office of International Education\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 14:27:08.046 INFO [http-nio-20000-exec-10] com.my.college.task.TaskEntrance:87 - 系统参数检测结果: 通过
2025-07-30 14:27:08.344 INFO [http-nio-20000-exec-10] c.m.c.service.impl.TaskLogServiceImpl:40 - [72/1950443020742680576/14583] 任务创建成功
2025-07-30 14:27:08.488 INFO [http-nio-20000-exec-10] c.m.c.service.impl.TaskLogServiceImpl:40 - [72/1950443020742680576/14583] 请求参数准备完毕
2025-07-30 14:27:09.135 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.TaskLogServiceImpl:40 - [72/1950443020742680576/14583] 接口调用成功, 等待响应
2025-07-30 14:27:09.265 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.TaskLogServiceImpl:40 - [72/1950443020742680576/14583] 响应成功
2025-07-30 14:27:09.387 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443025155088384] 子任务创建成功
2025-07-30 14:27:09.512 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443025155088384] markdown请求参数准备完毕
2025-07-30 14:27:09.626 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443026161721344] 子任务创建成功
2025-07-30 14:27:09.746 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443026161721344] markdown请求参数准备完毕
2025-07-30 14:27:09.857 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443027118022656] 子任务创建成功
2025-07-30 14:27:09.974 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443027118022656] markdown请求参数准备完毕
2025-07-30 14:27:10.088 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443028095295488] 子任务创建成功
2025-07-30 14:27:10.221 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443028095295488] markdown请求参数准备完毕
2025-07-30 14:27:10.333 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443029110317056] 子任务创建成功
2025-07-30 14:27:10.466 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443029110317056] markdown请求参数准备完毕
2025-07-30 14:27:10.590 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443030175670272] 子任务创建成功
2025-07-30 14:27:10.702 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443030175670272] markdown请求参数准备完毕
2025-07-30 14:27:10.826 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443031186497536] 子任务创建成功
2025-07-30 14:27:10.954 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443031186497536] markdown请求参数准备完毕
2025-07-30 14:27:11.074 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443032184741888] 子任务创建成功
2025-07-30 14:27:11.190 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443032184741888] markdown请求参数准备完毕
2025-07-30 14:27:11.302 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443033182986240] 子任务创建成功
2025-07-30 14:27:11.420 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443033182986240] markdown请求参数准备完毕
2025-07-30 14:27:11.542 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443034193813504] 子任务创建成功
2025-07-30 14:27:11.676 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443034193813504] markdown请求参数准备完毕
2025-07-30 14:27:11.745 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443025155088384] 接口调用成功, 等待响应
2025-07-30 14:27:11.868 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443025155088384] markdown响应成功
2025-07-30 14:27:12.003 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443025155088384] AI请求参数准备完毕
2025-07-30 14:27:14.075 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443026161721344] 接口调用成功, 等待响应
2025-07-30 14:27:14.195 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443026161721344] markdown响应成功
2025-07-30 14:27:14.326 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443026161721344] AI请求参数准备完毕
2025-07-30 14:27:16.175 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443027118022656] 接口调用成功, 等待响应
2025-07-30 14:27:16.316 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443027118022656] markdown响应成功
2025-07-30 14:27:16.439 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443027118022656] AI请求参数准备完毕
2025-07-30 14:27:18.861 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443028095295488] 接口调用成功, 等待响应
2025-07-30 14:27:19.010 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443028095295488] markdown响应成功
2025-07-30 14:27:19.142 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443028095295488] AI请求参数准备完毕
2025-07-30 14:27:22.479 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443029110317056] 接口调用成功, 等待响应
2025-07-30 14:27:22.623 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443029110317056] markdown响应成功
2025-07-30 14:27:22.761 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443029110317056] AI请求参数准备完毕
2025-07-30 14:27:24.968 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443032184741888] 接口调用成功, 等待响应
2025-07-30 14:27:24.969 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443030175670272] 接口调用成功, 等待响应
2025-07-30 14:27:25.188 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443032184741888] markdown响应成功
2025-07-30 14:27:25.200 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443030175670272] markdown响应成功
2025-07-30 14:27:25.327 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443032184741888] AI请求参数准备完毕
2025-07-30 14:27:25.335 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443030175670272] AI请求参数准备完毕
2025-07-30 14:27:26.169 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443025155088384] AI接口调用成功, 等待响应
2025-07-30 14:27:26.308 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443025155088384] AI响应成功
2025-07-30 14:27:26.410 WARN [ForkJoinPool.commonPool-worker-7] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@48466cda] Transaction not enabled
2025-07-30 14:27:26.546 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443025155088384] AI结果保存成功
2025-07-30 14:27:29.332 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443031186497536] 接口调用成功, 等待响应
2025-07-30 14:27:29.332 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443033182986240] 接口调用成功, 等待响应
2025-07-30 14:27:29.447 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443033182986240] markdown响应成功
2025-07-30 14:27:29.481 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443031186497536] markdown响应成功
2025-07-30 14:27:29.553 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443027118022656] AI接口调用成功, 等待响应
2025-07-30 14:27:29.584 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443033182986240] AI请求参数准备完毕
2025-07-30 14:27:29.655 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443031186497536] AI请求参数准备完毕
2025-07-30 14:27:29.731 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443027118022656] AI响应成功
2025-07-30 14:27:29.823 WARN [ForkJoinPool.commonPool-worker-1] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@498dd841] Transaction not enabled
2025-07-30 14:27:29.957 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443027118022656] AI结果保存成功
2025-07-30 14:27:31.461 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443034193813504] 接口调用成功, 等待响应
2025-07-30 14:27:31.582 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443034193813504] markdown响应成功
2025-07-30 14:27:31.698 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443034193813504] AI请求参数准备完毕
2025-07-30 14:27:37.165 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443030175670272] AI接口调用成功, 等待响应
2025-07-30 14:27:37.299 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443030175670272] AI响应成功
2025-07-30 14:27:37.398 WARN [ForkJoinPool.commonPool-worker-4] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@30a500e4] Transaction not enabled
2025-07-30 14:27:37.767 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443030175670272] AI结果保存成功
2025-07-30 14:27:37.910 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443034193813504] AI接口调用成功, 等待响应
2025-07-30 14:27:37.945 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443026161721344] AI接口调用成功, 等待响应
2025-07-30 14:27:38.041 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443034193813504] AI响应成功
2025-07-30 14:27:38.087 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443026161721344] AI响应成功
2025-07-30 14:27:38.185 WARN [ForkJoinPool.commonPool-worker-7] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@461ef51a] Transaction not enabled
2025-07-30 14:27:38.220 INFO [ForkJoinPool.commonPool-worker-0] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443034193813504] AI结果保存成功
2025-07-30 14:27:38.329 INFO [ForkJoinPool.commonPool-worker-7] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443026161721344] AI结果保存成功
2025-07-30 14:27:39.278 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443032184741888] AI接口调用成功, 等待响应
2025-07-30 14:27:39.415 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443032184741888] AI响应成功
2025-07-30 14:27:39.518 WARN [ForkJoinPool.commonPool-worker-2] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b9875d0] Transaction not enabled
2025-07-30 14:27:39.656 INFO [ForkJoinPool.commonPool-worker-2] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443032184741888] AI结果保存成功
2025-07-30 14:27:40.617 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443028095295488] AI接口调用成功, 等待响应
2025-07-30 14:27:40.746 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443028095295488] AI响应成功
2025-07-30 14:27:40.924 INFO [ForkJoinPool.commonPool-worker-1] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443028095295488] AI结果保存成功
2025-07-30 14:27:45.087 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443031186497536] AI接口调用成功, 等待响应
2025-07-30 14:27:45.232 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443031186497536] AI响应成功
2025-07-30 14:27:45.354 WARN [ForkJoinPool.commonPool-worker-6] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76f62fe4] Transaction not enabled
2025-07-30 14:27:45.498 INFO [ForkJoinPool.commonPool-worker-6] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443031186497536] AI结果保存成功
2025-07-30 14:27:50.690 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443033182986240] AI接口调用成功, 等待响应
2025-07-30 14:27:50.823 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443033182986240] AI响应成功
2025-07-30 14:27:50.919 WARN [ForkJoinPool.commonPool-worker-4] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2c87eb0d] Transaction not enabled
2025-07-30 14:27:51.057 INFO [ForkJoinPool.commonPool-worker-4] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443033182986240] AI结果保存成功
2025-07-30 14:28:10.663 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443029110317056] AI接口调用成功, 等待响应
2025-07-30 14:28:10.833 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443029110317056] AI响应成功
2025-07-30 14:28:10.957 WARN [ForkJoinPool.commonPool-worker-3] c.m.c.s.impl.AddressBookServiceImpl:166 - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@271e6a16] Transaction not enabled
2025-07-30 14:28:11.136 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.SubTaskLogServiceImpl:41 - [1950443020742680576/1950443029110317056] AI结果保存成功
2025-07-30 14:28:11.363 INFO [ForkJoinPool.commonPool-worker-3] c.m.c.service.impl.TaskLogServiceImpl:40 - [72/1950443020742680576/14583] 主任务处理完毕
2025-07-30 14:29:09.814 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiModel = Qwen/Qwen3-14B
2025-07-30 14:29:09.911 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiSystemPrompt = -
2025-07-30 14:29:10.001 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiUserPrompt = Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:"Office of International Education" 仅抓取以下职位:"Associate Director" OR "Director" OR "Faculty-led program director"</user_request><schema_block>{  "properties": {    "contactName": {       "description": "The contactName of the entity.",      "title": "contactName",      "type": "string" },    "department": {      "description": "The department of the entity.",      "title": "department",      "type": "string"    },    "position": {      "description": "The position of the  entity.",      "title": "position",      "type": "string"    },    "email": {      "description": "The email of the entity.",      "title": "email",      "type": "string"    },    "phone": {      "description": "The phone of the entity.",      "title": "phone",      "type": "string"    }  },  "required": [    "contactName" ],  "title": "BusinessData",  "type": "object"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using "//" or  "#" in the JSON output. - Do NOT using "```json" and "```" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.
2025-07-30 14:29:10.091 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiApiKey = sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud
2025-07-30 14:29:10.174 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiTimeout = 300
2025-07-30 14:29:10.276 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: markdownTimeout = 10
2025-07-30 14:29:10.382 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleApiKey = AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc
2025-07-30 14:29:10.474 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleEngineID = 66818a77d1c67403b
2025-07-30 14:29:10.576 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleKeyword = site:学校网址  (intext:"Office of International Education") (intext:"@学校网址" OR intext:"email" OR intext:"contact") -inurl:("archive" | "old" | "pdf") -filetype:pdf -filetype:xlsx -filetype:doc
2025-07-30 14:29:10.577 INFO [http-nio-20000-exec-2] c.m.c.service.impl.SysParamServiceImpl:73 - 系统参数批量更新完成
2025-07-30 14:29:10.610 INFO [http-nio-20000-exec-2] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:\"Office of International Education\" 仅抓取以下职位:\"Associate Director\" OR \"Director\" OR \"Faculty-led program director\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址  (intext:\"Office of International Education\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 14:29:39.463 INFO [http-nio-20000-exec-6] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiModel = Qwen/Qwen3-14B
2025-07-30 14:29:39.551 INFO [http-nio-20000-exec-6] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiSystemPrompt = -
2025-07-30 14:29:39.640 INFO [http-nio-20000-exec-6] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiUserPrompt = Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:"Office of International Education" 仅抓取以下职位:"Associate Director" OR "Director" OR "Faculty-led program director"</user_request><schema_block>{  "properties": {    "contactName": {       "description": "The contactName of the entity.",      "title": "contactName",      "type": "string" },    "department": {      "description": "The department of the entity.",      "title": "department",      "type": "string"    },    "position": {      "description": "The position of the  entity.",      "title": "position",      "type": "string"    },    "email": {      "description": "The email of the entity.",      "title": "email",      "type": "string"    },    "phone": {      "description": "The phone of the entity.",      "title": "phone",      "type": "string"    }  },  "required": [    "contactName" ],  "title": "BusinessData",  "type": "object"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using "//" or  "#" in the JSON output. - Do NOT using "```json" and "```" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.
2025-07-30 14:29:39.732 INFO [http-nio-20000-exec-6] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiApiKey = sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud
2025-07-30 14:29:39.814 INFO [http-nio-20000-exec-6] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: aiTimeout = 300
2025-07-30 14:29:39.894 INFO [http-nio-20000-exec-6] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: markdownTimeout = 10
2025-07-30 14:29:39.986 INFO [http-nio-20000-exec-6] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleApiKey = AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc
2025-07-30 14:29:40.078 INFO [http-nio-20000-exec-6] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleEngineID = 66818a77d1c67403b
2025-07-30 14:29:40.170 INFO [http-nio-20000-exec-6] c.m.c.service.impl.SysParamServiceImpl:70 - 更新系统参数: googleKeyword = site:学校网址 (intitle:"contact us" OR "directory" OR "staff") (intext:"Office of International Education") (intext:"@学校网址" OR intext:"email" OR intext:"contact") -inurl:("archive" | "old" | "pdf") -filetype:pdf -filetype:xlsx -filetype:doc
2025-07-30 14:29:40.170 INFO [http-nio-20000-exec-6] c.m.c.service.impl.SysParamServiceImpl:73 - 系统参数批量更新完成
2025-07-30 14:29:40.202 INFO [http-nio-20000-exec-6] com.my.college.cache.SysParamCache:66 - 系统参数缓存重载完成. SysParam: {"aiApiKey":"sk-fsqleewxasjoujjpodidbgpnkmidjzkfmnnipfvedybltjud","aiModel":"Qwen/Qwen3-14B","aiSystemPrompt":"-","aiTimeout":"300","aiUserPrompt":"Here is the content:<url_content>${content}</url_content>The user has made the following request for what information to extract from the above content:<user_request>从内容中抓取以下信息：联系人名字、部门、职位、email、电话, 存入以下变量 contactName、department、position、email、phone.仅抓取以下部门:\"Office of International Education\" 仅抓取以下职位:\"Associate Director\" OR \"Director\" OR \"Faculty-led program director\"</user_request><schema_block>{  \"properties\": {    \"contactName\": {       \"description\": \"The contactName of the entity.\",      \"title\": \"contactName\",      \"type\": \"string\" },    \"department\": {      \"description\": \"The department of the entity.\",      \"title\": \"department\",      \"type\": \"string\"    },    \"position\": {      \"description\": \"The position of the  entity.\",      \"title\": \"position\",      \"type\": \"string\"    },    \"email\": {      \"description\": \"The email of the entity.\",      \"title\": \"email\",      \"type\": \"string\"    },    \"phone\": {      \"description\": \"The phone of the entity.\",      \"title\": \"phone\",      \"type\": \"string\"    }  },  \"required\": [    \"contactName\" ],  \"title\": \"BusinessData\",  \"type\": \"object\"}</schema_block>Please carefully read the URL content and the user request. If the user provided a desired JSON schema in the <schema_block> above, extract the requested information from the URL content according to that schema.  If no schema was provided, infer an appropriate JSON schema based on the user request that will best capture the key information they are looking for. Extraction instructions: Return the extracted information as a list of JSON objects, with each object in the list corresponding to a block of content from the URL, in the same order as it appears on the page.  Quality Reflection:Before outputting your final answer, double check that the JSON you are returning is complete, containing all the information requested by the user, and is valid JSON that could be parsed by json.loads() with no errors or omissions. The outputted JSON objects should fully match the schema, either provided or inferred. Avoid Common Mistakes: - Do NOT add any comments using \"//\" or  \"#\" in the JSON output. - Do NOT using \"```json\" and \"```\" wrapper the JSON output. It causes parsing errors. - Make sure the JSON is properly formatted with  square brackets, and commas in the right places. ResultOutput: the final list of  JSON objects.","googleApiKey":"AIzaSyB-Ht9x7NEruKTSNUHxVLWdhYEwSmFPvtc","googleEngineID":"66818a77d1c67403b","googleKeyword":"site:学校网址 (intitle:\"contact us\" OR \"directory\" OR \"staff\") (intext:\"Office of International Education\") (intext:\"@学校网址\" OR intext:\"email\" OR intext:\"contact\") -inurl:(\"archive\" | \"old\" | \"pdf\") -filetype:pdf -filetype:xlsx -filetype:doc","markdownTimeout":"10"}
2025-07-30 14:29:48.576 INFO [http-nio-20000-exec-10] com.my.college.task.TaskEntrance:87 - 系统参数检测结果: 通过
2025-07-30 14:29:48.870 INFO [http-nio-20000-exec-10] c.m.c.service.impl.TaskLogServiceImpl:40 - [73/1950443694003970048/14584] 任务创建成功
2025-07-30 14:29:49.010 INFO [http-nio-20000-exec-10] c.m.c.service.impl.TaskLogServiceImpl:40 - [73/1950443694003970048/14584] 请求参数准备完毕
2025-07-30 14:29:49.491 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.TaskLogServiceImpl:40 - [73/1950443694003970048/14584] 接口调用成功, 等待响应
2025-07-30 14:29:49.598 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.TaskLogServiceImpl:40 - [73/1950443694003970048/14584] 响应成功
2025-07-30 14:29:49.717 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.TaskLogServiceImpl:40 - [73/1950443694003970048/14584] 主任务处理完毕
2025-07-30 14:30:00.935 INFO [http-nio-20000-exec-1] com.my.college.task.TaskEntrance:87 - 系统参数检测结果: 通过
2025-07-30 14:30:01.191 INFO [http-nio-20000-exec-1] c.m.c.service.impl.TaskLogServiceImpl:40 - [74/1950443745732321280/14585] 任务创建成功
2025-07-30 14:30:01.305 INFO [http-nio-20000-exec-1] c.m.c.service.impl.TaskLogServiceImpl:40 - [74/1950443745732321280/14585] 请求参数准备完毕
2025-07-30 14:30:01.853 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.TaskLogServiceImpl:40 - [74/1950443745732321280/14585] 接口调用成功, 等待响应
2025-07-30 14:30:01.984 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.TaskLogServiceImpl:40 - [74/1950443745732321280/14585] 响应成功
2025-07-30 14:30:02.115 INFO [ForkJoinPool.commonPool-worker-5] c.m.c.service.impl.TaskLogServiceImpl:40 - [74/1950443745732321280/14585] 主任务处理完毕
2025-07-30 17:22:09.217 WARN [RMI TCP Connection(2)-127.0.0.1] o.a.c.loader.WebappClassLoaderBase:173 - The web application [ROOT] appears to have started a thread named [Thread-3] but has failed to stop it. This is very likely to create a memory leak. Stack trace of thread:
 java.lang.Thread.sleep(Native Method)
 cn.dev33.satoken.dao.SaTokenDaoDefaultImpl.lambda$initRefreshThread$0(SaTokenDaoDefaultImpl.java:247)
 cn.dev33.satoken.dao.SaTokenDaoDefaultImpl$$Lambda$679/1829242067.run(Unknown Source)
 java.lang.Thread.run(Unknown Source)
