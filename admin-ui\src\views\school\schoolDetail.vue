<template>
  <!-- 查看 -->
  <el-dialog title="学校详情" :visible.sync="detailFormVisible" width="500px" @click="closeDialog" class="school-detail-dialog">
    <div class="detail-content">
      <el-form label-width="100px" :model="detailForm" ref="detailForm">
        <el-form-item label="编号">
          <span class="detail-value">{{ detailForm.schoolId }}</span>
        </el-form-item>
        <el-form-item label="省份">
          <span class="detail-value">{{ detailForm.provinceId }} - {{ getProvinceName(detailForm.provinceId) }}</span>
        </el-form-item>
        <el-form-item label="中文名称">
          <span class="detail-value">{{ detailForm.chineseName }}</span>
        </el-form-item>
        <el-form-item label="英文名称">
          <span class="detail-value">{{ detailForm.englishName }}</span>
        </el-form-item>
        <el-form-item label="学校网址">
          <el-link type="primary" :href="detailForm.website" target="_blank" v-if="detailForm.website">
            {{ detailForm.website }}
          </el-link>
          <span class="detail-value" v-else>{{ detailForm.website }}</span>
        </el-form-item>
        <el-form-item label="备注">
          <div class="remark-box">
            {{ detailForm.remark || '无' }}
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { provinceList } from '../../api/province'

export default {
  name: 'schoolDetail',
  props: ['childMsg'],
  data() {
    return {
      detailFormVisible: false,
      detailForm: {
        schoolId: '',
        provinceId: '',
        chineseName: '',
        englishName: '',
        website: '',
        remark: ''
      },
      provinceList: [] //省份列表
    }
  },
  created() {
    // 加载省份列表
    this.loadProvinceList();
  },
  methods: {
    // 加载省份列表
    loadProvinceList() {
      provinceList().then(res => {
        this.provinceList = res.data || [];
      });
    },
    // 根据provinceId获取省份名称
    getProvinceName(provinceId) {
      if (!provinceId) return '未设置';
      const province = this.provinceList.find(item => item.provinceId === provinceId);
      return province ? province.chineseName : '未知省份';
    },
    closeDialog() {
      this.detailFormVisible = false
    },
    show(row) {
      this.detailFormVisible = true;
      this.resetForm();
      if (row) {
        Object.assign(this.detailForm, row);
      }
    },
    resetForm() {
      this.detailForm = {
        schoolId: '',
        provinceId: '',
        chineseName: '',
        englishName: '',
        website: '',
        remark: ''
      };
    }
  }
}
</script>

<style>
.el-dialog .el-dialog__body {
  padding: 0px 20px !important;
}

.school-detail-dialog .el-dialog__body {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 20px !important;
}

.detail-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.detail-content .el-form-item {
  margin-bottom: 18px;
}

.detail-content .el-form-item__label {
  font-weight: 600;
  color: #606266;
}

.detail-value {
  color: #303133;
  font-size: 14px;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  display: inline-block;
  min-width: 200px;
}

.remark-box {
  min-height: 60px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #606266;
  line-height: 1.5;
}
</style>
