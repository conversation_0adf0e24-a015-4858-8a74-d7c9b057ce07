<template>
  <div>
    <!-- breadcrumb -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">通讯录管理</el-breadcrumb-item>
      <el-breadcrumb-item>通讯录</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- search -->
    <el-form :inline="true" :model="formInline" class="user-search" ref="searchForm">
      <el-form-item label="查询：">
        <el-select size="small" v-model="formInline.schoolId" placeholder="选择学校" clearable filterable style="width: 120px;">
          <el-option
            v-for="school in schoolList"
            :key="school.schoolId"
            :label="`${school.schoolId} - ${school.englishName}`"
            :value="school.schoolId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="formInline.department" placeholder="部门" clearable style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="formInline.contactName" placeholder="联系人姓名" clearable style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="formInline.email" placeholder="邮箱" clearable style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="formInline.phone" placeholder="电话" clearable style="width: 100px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select size="small" v-model="formInline.manualFlag" placeholder="自动获取" clearable style="width: 100px;">
          <el-option label="是" value="false"></el-option>
          <el-option label="否" value="true"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" icon="el-icon-search" @click="search">查询</el-button>
        <el-button size="small" type="primary" icon="el-icon-refresh" @click="resetSearch()">重置</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="handleAdd" v-if="userEntity && userEntity.adminFlag">新增</el-button>
        <el-button size="small" type="danger" icon="el-icon-delete" @click="handleBatchDelete" v-if="userEntity && userEntity.adminFlag && multipleSelection.length > 0">删除</el-button>
        <el-button size="small" type="success" icon="el-icon-upload2" @click="handleImport" v-if="userEntity && userEntity.adminFlag">导入</el-button>
        <el-button size="small" type="warning" icon="el-icon-download" @click="handleExport">导出</el-button>
      </el-form-item>
    </el-form>

    <!--list-->
    <el-table ref="ids" @sort-change="getSortPageData" @selection-change="handleSelectionChange" @row-click="handleRowClick" :data="listData" highlight-current-row v-loading="loading" border element-loading-text="loading" style="width: 100%;">
      <el-table-column type="selection" width="40" v-if="userEntity && userEntity.adminFlag"></el-table-column>
      <el-table-column prop="id" label="编号" width="50" show-overflow-tooltip></el-table-column>
      <el-table-column prop="schoolId" label="学校" width="150" show-overflow-tooltip sortable="custom">
        <template slot-scope="scope">
          {{ getSchoolName(scope.row.schoolId) }}
        </template>
      </el-table-column>
      <el-table-column prop="department" label="部门" width="160" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column prop="position" label="职位" width="150" sortable="custom" show-overflow-tooltip></el-table-column>
      <el-table-column prop="contactName" label="联系人姓名" width="180" sortable="custom" show-overflow-tooltip></el-table-column>
      <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column prop="phone" label="电话" width="120" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column prop="url" label="来源" width="50" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link :href="scope.row.url" target="_blank" :underline="false">
            <i class="el-icon-link"></i>
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="manualFlag" label="自动获取" width="50" align="center">
        <template slot-scope="scope">
          <el-tag :type="!scope.row.manualFlag ? 'success' : 'info'">
            {{ !scope.row.manualFlag ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" width="170" sortable="custom"></el-table-column>
      <el-table-column align="left" label="操作" min-width="140">
        <template slot-scope="scope">
          <el-link>
            <i class="el-icon-view" @click="handleView(scope.row)"></i>
          </el-link>
          <el-link v-if="userEntity && userEntity.adminFlag">
            <i class="el-icon-edit" @click="handleEdit(scope.row)"></i>
          </el-link>
          <el-link v-if="userEntity && userEntity.adminFlag">
            <i class="el-icon-delete" @click="handleDelete(scope.row)"></i>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- page -->
    <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>

    <!-- 子组件 -->
    <addressBookInsert @callback="getPageData" ref="addressBookInsert"></addressBookInsert>
    <addressBookUpdate @callback="getPageData" ref="addressBookUpdate"></addressBookUpdate>
    <addressBookDetail ref="addressBookDetail"></addressBookDetail>
    <addressBookUpload @refreshData="getPageData" ref="addressBookUpload"></addressBookUpload>
  </div>
</template>

<script>
import { addressBookPage, deleteAddressBook, getSchoolList, adressBookDownload } from '../../api/addressBook'
import qs from 'qs';
import Pagination from '../../components/Pagination'
import { loadToken } from '../../utils/util'
import addressBookInsert from './addressBookInsert'
import addressBookUpdate from './addressBookUpdate'
import addressBookDetail from './addressBookDetail'
import addressBookUpload from './addressBookUpload'

export default {
  data() {
    return {
      loading: false,
      formInline: {
        current: 1,
        size: 10,
        contactName: undefined,
        email: undefined,
        phone: undefined,
        department: undefined,
        schoolId: undefined,
        manualFlag: undefined,
        token: loadToken(),
        orders: [],
      },
      userEntity: undefined,
      listData: [], //分页数据
      schoolList: [], //学校列表
      multipleSelection: [], //多选数据
      // page param
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      }
    }
  },
  components: {
    Pagination,
    addressBookInsert,
    addressBookUpdate,
    addressBookDetail,
    addressBookUpload
  },

  created() {
    // 加载用户缓存
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'));
    // 加载学校列表
    this.getSchoolList();
    // 分页数据
    this.getPageData()
  },
  methods: {
    // 获取学校列表
    getSchoolList() {
      getSchoolList()
        .then(res => {
          this.schoolList = res.data;
        })
    },
    // 获取学校名称
    getSchoolName(schoolId) {
      const school = this.schoolList.find(s => s.schoolId === schoolId);
      return school ? school.englishName : '未知学校';
    },
    // page-1: page
    getPageData(parameter) {
      this.loading = true
      if (!parameter){
        parameter = this.formInline;
      }
      addressBookPage(parameter)
        .then(res => {
          this.loading = false;
          this.listData = res.data.records
          this.pageParam.currentPage = res.data.current;
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total
        })
    },
    // page-2: callBack
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },
    // page-3: page && sort
    getSortPageData(column){
      if (column.order != null){
        let sortProp = column.prop.replace(/([A-Z])/g, "_$1").toLowerCase();
        let orderBy = {"column": sortProp, "asc": column.order == 'ascending'};
        this.formInline.orders[0] = orderBy;
      } else {
        this.formInline.orders = [];
      }
      this.getPageData();
    },
    // search-1
    search() {
      this.getPageData()
    },
    // search-2
    resetSearch() {
      this.formInline.contactName = undefined
      this.formInline.email = undefined
      this.formInline.phone = undefined
      this.formInline.department = undefined
      this.formInline.schoolId = undefined
      this.formInline.manualFlag = undefined
    },
    handleAdd() {
      this.$refs.addressBookInsert.show();
    },
    handleImport() {
      this.$refs.addressBookUpload.show();
    },
    handleExport() {
      this.$confirm('确认导出当前查询到的数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        const params = qs.stringify(this.formInline, { allowDots: true, skipNulls: true })
        let url = "/api/address-book/download?" + params
        window.open(url);
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导出'
        });
      });
    },
    handleView(row) {
      this.$refs.addressBookDetail.show(row);
    },
    handleEdit(row) {
      this.$refs.addressBookUpdate.show(row);
    },
    handleDelete(row) {
      this.$confirm('确认删除该通讯录记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAddressBook({idList: [row.id]})
          .then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getPageData();
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 行点击事件，切换复选框选中状态
    handleRowClick(row, column, event) {
      // 只有在有管理员权限且显示复选框时才执行
      if (this.userEntity && this.userEntity.adminFlag) {
        this.$refs.ids.toggleRowSelection(row);
      }
    },
    handleBatchDelete() {
      this.$confirm('确认删除选中的通讯录记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.multipleSelection.map(item => item.id);
        const promises = this.multipleSelection.map(item => deleteAddressBook({idList: ids}));
        Promise.all(promises)
          .then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getPageData();
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    }
  }
}
</script>

<style scoped>
.user-search {
  margin-top: 20px;
}

.user-search .el-input--small {
    width: 180px!important;
}

.el-link {
  margin-left: 10px!important;
  cursor: pointer!important;
}

.inline-edit-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.inline-edit-form .el-form-item {
  margin-bottom: 0;
}
</style>
