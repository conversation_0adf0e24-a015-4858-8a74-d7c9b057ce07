<template>
  <div>
    <!-- breadcrumb -->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/' }">系统管理</el-breadcrumb-item>
      <el-breadcrumb-item>任务记录</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- search -->
    <el-form :inline="true" :model="formInline" class="user-search" ref="searchForm">
      <el-form-item label="查询：">
        <el-select size="small" v-model="formInline.schoolId" placeholder="选择学校" clearable filterable
          style="width: 150px;">
          <el-option v-for="school in schoolList" :key="school.schoolId"
            :label="`${school.schoolId} - ${school.englishName}`" :value="school.schoolId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input size="small" v-model="formInline.batchNumber" placeholder="批次号" maxlength="6" clearable
          style="width: 90px!important;"></el-input>
      </el-form-item>
      <!--
      <el-form-item>
        <el-input size="small" v-model="formInline.taskId" placeholder="任务编号" clearable
          style="width: 130px!important;"></el-input>
      </el-form-item> -->
      <el-form-item>
        <el-date-picker size="small" v-model="formInline.startTime" type="datetime" placeholder="开始时间"
          value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 185px!important;">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-date-picker size="small" v-model="formInline.endTime" type="datetime" placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 185px!important;">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select size="small" v-model="formInline.status" placeholder="主任务状态" clearable style="width: 120px;">
          <el-option label="运行中" value="RUN"></el-option>
          <el-option label="失败" value="FAIL"></el-option>
          <el-option label="成功" value="SUCCESS"></el-option>
          <el-option label="已重试" value="REBORN"></el-option>
          <el-option label="中断" value="REBORN"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item>
        <el-select size="small" v-model="formInline.status" placeholder="子任务状态" clearable style="width: 120px;">
          <el-option label="运行中" value="RUN"></el-option>
          <el-option label="失败" value="FAIL"></el-option>
          <el-option label="成功" value="SUCCESS"></el-option>
          <el-option label="已重试" value="REBORN"></el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button size="small" type="primary" icon="el-icon-search" @click="search">查询</el-button>
        <el-button size="small" type="primary" icon="el-icon-refresh" @click="resetSearch()">重置</el-button>
        <el-button size="small" type="success" icon="el-icon-plus" @click="goBatchTaskCreate">创建任务</el-button>
      </el-form-item>
    </el-form>

    <!--list-->
    <el-table size="small" ref="ids" @sort-change="getSortPageData" :data="listData" highlight-current-row
      v-loading="loading" border element-loading-text="loading" style="width: 100%;"
      :expand-row-keys="expandedRows" row-key="taskId">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div style="padding: 5px 15px;">
            <!-- <h4>子任务记录</h4> -->
            <subTaskRecord :taskId="props.row.taskId"></subTaskRecord>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="batchNumber" label="批次号" width="60"></el-table-column>
      <el-table-column prop="taskId" label="任务编号" width="90" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link type="primary" @click="toggleExpand(scope.row)" :underline="false">
            {{ scope.row.taskId }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="schoolId" label="学校" width="180" show-overflow-tooltip sortable="custom">
        <template slot-scope="scope">
          {{ getSchoolName(scope.row.schoolId) }}
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="开始时间" width="150" sortable="custom"></el-table-column>
      <el-table-column prop="endTime" label="结束时间" width="150" sortable="custom"></el-table-column>
      <el-table-column prop="request" label="请求 (谷歌参数)" width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="response" label="响应 (联系人网址)" width="180" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column prop="finishTime" label="完成时间" width="150" sortable="custom"></el-table-column> -->
      <!-- <el-table-column prop="createTime" label="创建时间" width="150" sortable="custom"></el-table-column> -->
      <el-table-column prop="status" label="任务状态" width="100" sortable="custom">
        <template slot-scope="scope">
          <el-tooltip v-if="scope.row.status === 'FAIL' && scope.row.failRemark"
                      :content="scope.row.failRemark"
                      placement="top">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </el-tooltip>
          <el-tag v-else :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="left" label="操作" min-width="100">
        <template slot-scope="scope">
          <el-link>
            <i class="el-icon-receiving" @click="popWin('taskRecordDetail', scope.row)"> 详情</i>
          </el-link>
          <el-link v-if="scope.row.status === 'FAIL' || scope.row.status === 'STOP'">
            <i class="el-icon-refresh" @click="retryTask(scope.row)"> 重试</i>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- page -->
    <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>

    <!-- Detail -->
    <taskRecordDetail @callback="getPageData" ref="taskRecordDetail"></taskRecordDetail>
  </div>
</template>

<script>
import { taskRecordPage, taskRecordReborn } from '../../api/taskRecord'
import { getSchoolList } from '../../api/addressBook'
import qs from 'qs';
import Pagination from '../../components/Pagination'
import { loadToken } from '../../utils/util'
import taskRecordDetail from './taskRecordDetail'
import subTaskRecord from './subTaskRecord'

export default {
  data() {
    return {
      loading: false,
      formInline: {
        current: 1,
        size: 10,
        schoolId: undefined,   // 学校ID
        taskId: undefined,     // 任务编号
        status: undefined,     // 任务状态
        batchNumber: undefined, // 批次号
        startTime: undefined, // 开始时间（起）
        endTime: undefined,   // 开始时间（止）
        token: loadToken(),
        orders: [],
      },
      userEntity: undefined,
      listData: [], //分页数据
      // page param
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      schoolList: [],
      expandedRows: []  // 展开的行
    }
  },
  components: {
    Pagination,
    taskRecordDetail,
    subTaskRecord
  },

  created() {
    // 加载用户缓存
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'));
    // 加载学校列表
    this.getSchoolList();
    // 分页数据
    this.getPageData()
  },
  methods: {
    // 获取状态显示样式
    getStatusType(status) {
      switch (status) {
        case 'RUN': return 'primary';
        case 'FAIL': return 'danger';
        case 'SUCCESS': return 'success';
        case 'REBORN': return 'info';
        case 'STOP': return 'warning';
        default: return 'info';
      }
    },
    // 获取状态显示文本
    getStatusText(status) {
      switch (status) {
        case 'RUN': return '运行中';
        case 'FAIL': return '失败';
        case 'SUCCESS': return '成功';
        case 'REBORN': return '已重试';
        case 'STOP': return '中断';
        default: return status;
      }
    },
    // page-1: page
    getPageData(parameter) {
      this.loading = true
      if (!parameter) {
        parameter = this.formInline;
      }
      taskRecordPage(parameter)
        .then(res => {
          this.loading = false;
          this.listData = res.data.records
          this.pageParam.currentPage = res.data.current;
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total
        })
    },
    // page-2: callBack
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },
    // page-3: page && sort
    getSortPageData(column) {
      if (column.order != null) {
        let sortProp = column.prop.replace(/([A-Z])/g, "_$1").toLowerCase();
        let orderBy = { "column": sortProp, "asc": column.order == 'ascending' };
        this.formInline.orders[0] = orderBy;
      } else {
        this.formInline.orders = [];
      }
      this.getPageData();
    },
    // search-1
    search() {
      // 查询时折叠所有展开的子任务表
      this.expandedRows = []
      this.getPageData()
    },
    // search-2
    resetSearch() {
      this.formInline.schoolId = undefined    // 学校ID
      this.formInline.taskId = undefined       // 任务编号
      this.formInline.status = undefined       // 任务状态
      this.formInline.batchNumber = undefined // 批次号
      this.formInline.startTime = undefined  // 开始时间（起）
      this.formInline.endTime = undefined    // 开始时间（止）
    },
    // 弹窗
    popWin(windownName, data) {
      if (this.$refs[windownName]) {
        this.$refs[windownName].show(data);
      }
    },
    // 批量创建任务
    goBatchTaskCreate() {
      this.$router.push('/batchTaskCreate');
    },
    // 获取学校列表
    getSchoolList() {
      getSchoolList().then(res => {
        this.schoolList = res.data;
      });
    },
    // 获取学校名称
    getSchoolName(schoolId) {
      const school = this.schoolList.find(s => s.schoolId === schoolId);
      return school ? school.englishName : '未知学校';
    },
    // 重试任务
    retryTask(task) {
      this.$confirm(`确定要重试任务 ${task.taskId} 吗？`, '确认重试', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.doRetryTask(task);
      }).catch(() => {
        this.$message.info('已取消重试');
      });
    },
    // 执行重试任务
    doRetryTask(task) {
      const params = {
        taskId: task.taskId,
        token: loadToken()
      };

      taskRecordReborn(params)
        .then(res => {
          this.$message.success('任务重试成功');
          // 刷新列表
          this.getPageData();
        })
        .catch(error => {
          console.error('重试任务失败:', error);
          this.$message.error('重试任务失败');
        });
    },
    // 切换展开状态
    toggleExpand(row) {
      const index = this.expandedRows.indexOf(row.taskId);
      if (index > -1) {
        // 已展开，收起
        this.expandedRows.splice(index, 1);
      } else {
        // 未展开，展开
        this.expandedRows.push(row.taskId);
      }
    }
  }
}
</script>

<style scoped>
.user-search {
  margin-top: 20px;
}

.user-search .el-input--small {
  width: 180px !important;
}

.el-link {
  margin-left: 10px !important;
  cursor: pointer !important;
}

.inline-edit-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.inline-edit-form .el-form-item {
  margin-bottom: 0;
}

/* 展开行样式 */
.el-table__expanded-cell {
  padding: 20px 50px !important;
  background-color: #fafafa;
}

.el-table__expanded-cell h4 {
  margin-bottom: 15px;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}
</style>
