<template>
  <!-- 详情 -->
  <el-dialog title="上传学校" :visible.sync="editFormVisible" width="20%" @click="closeDialog">
    <el-form label-width="120px" :model="uploadForm" ref="uploadForm">
        <el-form-item label="模板">
          <a :href='"./static/school-upload-templdate.xlsx"'>下载模板</a>
        </el-form-item>
        <hr/>
        <el-form-item label="上传excel" prop="excelUrl">
          <el-upload
            class="uploadFile"
            name="file"
            action="#"
            ref="uploadingExcel"
            :multiple="false"
            :limit="1"
            :auto-upload="false"
            :on-change="doUploadExcel"
            accept=".xls,.xlsx">
             <el-button type="text" size="small" icon="el-icon-upload">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <div class="error-title" v-if="uploadForm.totalCnt">
          成功:{{uploadForm.successCnt}},
          失败:{{uploadForm.failCnt}},
          总记录数：{{uploadForm.totalCnt}}
        </div>
        <ul class="uploadErr">
          <li v-for="item in uploadForm.errList" :key="item">{{item}}</li>
        </ul>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeExcelForm">关闭</el-button>
      </div>
  </el-dialog>
</template>

<script>
import { schoolUpload } from '@/api/school';

export default {
  name: 'SchoolUpload',
  props: ['childMsg', 'districtName'],
  data() {
    return {
      loading: false,
      editFormVisible: false,
      uploadForm: {
        errList: [],
        totalCnt: 0,
        successCnt: 0,
        failCnt: 0
      }
    }
  },

  created() {
  },

  // 方法
  methods: {
    closeDialog() {
      this.editFormVisible = false
    },
    closeExcelForm() {
      this.editFormVisible = false;
      this.$refs.uploadingExcel.clearFiles();
    },
    show(row) {
      this.editFormVisible = true;
      this.loading = false
      this.uploadForm.totalCnt = null;
      this.uploadForm.successCnt = null;
      this.uploadForm.failCnt = null;
      this.uploadForm.errList = [];
      // row中可能已经包含districtName，直接赋值
      Object.assign(this.uploadForm, row);
    },
    // excel上传
    doUploadExcel(file, fileList){
        this.uploadForm.errList.length = 0;
        let _this = this;
        let formData = new FormData();
        formData.append('file', file.raw);
        schoolUpload(formData).then((res) => {
          if (res.success && res.data) {
            this.uploadForm.totalCnt = res.data.totalCnt;
            this.uploadForm.successCnt = res.data.validCnt;
            this.uploadForm.failCnt = res.data.invalidCnt;
            if (res.data.valid) {
              setTimeout(() => {
                this.$emit('refreshData');
                this.closeExcelForm();
                this.$message({type: 'success', message: '上传成功'});
              }, 3000);
              return;
            } else {
              res.data.errList.forEach(err => {
                this.uploadForm.errList.push(err);
              })
              _this.$refs.uploadingExcel.clearFiles();
            }
          } else {
            this.$message.error('文件上传失败!')
          }
        })
    },
  }
}
</script>

<style>
.el-dialog .el-dialog__body {
  padding: 0px 20px !important;
}

.bottom-space {
  height: 20px;
  width: 100%;
}
</style>