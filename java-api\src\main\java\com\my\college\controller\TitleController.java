package com.my.college.controller;


import org.springframework.web.bind.annotation.RequestMapping;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import com.my.college.controller.dto.title.TitleInsertDTO;
import com.my.college.controller.dto.title.TitlePageDTO;
import com.my.college.controller.dto.title.TitleUpdateDTO;
import com.my.college.controller.dto.title.TitleDeleteDTO;
import com.my.college.controller.vo.title.TitleDetailVO;
import com.my.college.controller.vo.title.TitlePageVO;
import com.my.college.controller.vo.StdResp;
import com.my.college.mybatis.entity.Title;

import com.my.college.service.TitleService;
import java.util.List;

import org.springframework.web.bind.annotation.RestController;

/**
 * 标题表 
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/api/title")
@RequiredArgsConstructor
public class TitleController {

    private final TitleService titleService;

    /**
    * 新增
    */
    @PostMapping
    public StdResp<?> insert(@Valid @RequestBody TitleInsertDTO insertDTO) {
    	this.titleService.insert(insertDTO);
    	return StdResp.success();
    }

    /**
    * 修改
    */
    @PutMapping
    public StdResp<?> update(@Valid @RequestBody TitleUpdateDTO updateDTO) {
    	this.titleService.update(updateDTO);
    	return StdResp.success();
    }
            
    /**
    * 根据主键查询
    * @param id 主键
    */
    @GetMapping("/{id}")
    public StdResp<TitleDetailVO> detail(@PathVariable String id) {
    	return StdResp.success(this.titleService.detail(id));
    }
	
    /**
     * 分页
     */
    @GetMapping(value = "page")
    public StdResp<Page<TitlePageVO>> page(TitlePageDTO pageDTO) {
        Page<TitlePageVO> page = this.titleService.page(pageDTO);
        return StdResp.success(page);
    }

    /**
     * 查询全部标题
     */
    @GetMapping(value = "list")
    public StdResp<List<Title>> list() {
    	List<Title> data = this.titleService.list();
    	return StdResp.success(data);
    }

    /**
    * 批量删除(物理删除)
    */
    @DeleteMapping
    public StdResp<?> delete(@Valid @RequestBody TitleDeleteDTO deleteDTO) {
    	this.titleService.delete(deleteDTO);
		return StdResp.success();
    }

}
