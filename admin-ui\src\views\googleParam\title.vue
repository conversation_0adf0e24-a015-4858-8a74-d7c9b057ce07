<template>
  <div>
    <!-- search -->
    <el-form :inline="true" :model="formInline" class="user-search" ref="searchForm">
      <el-form-item label="查询：">
        <el-input size="small" v-model="formInline.title" placeholder="标题" clearable style="width: 200px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" icon="el-icon-search" @click="search">查询</el-button>
        <el-button size="small" type="primary" icon="el-icon-refresh" @click="resetSearch()">重置</el-button>
        <el-button size="small" type="primary" icon="el-icon-plus" @click="handleAdd" v-if="userEntity && userEntity.adminFlag">新增</el-button>
        <el-button size="small" type="danger" icon="el-icon-delete" @click="handleBatchDelete" v-if="userEntity && userEntity.adminFlag && multipleSelection.length > 0">删除</el-button>
      </el-form-item>
    </el-form>

    <!--list-->
    <el-table ref="ids" @sort-change="getSortPageData" @selection-change="handleSelectionChange" :data="listData" highlight-current-row v-loading="loading" border element-loading-text="loading" style="width: 40%;">
      <el-table-column type="selection" width="39" v-if="userEntity && userEntity.adminFlag"></el-table-column>
      <el-table-column prop="title" label="标题" show-overflow-tooltip sortable="custom"></el-table-column>
      <el-table-column align="left" label="操作" width="80">
        <template slot-scope="scope">
          <el-link v-if="userEntity && userEntity.adminFlag">
            <i class="el-icon-delete" @click="handleDelete(scope.row)"></i>
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- page -->
    <Pagination v-bind:child-msg="pageParam" @callback_getPageData="callback_getPageData"></Pagination>

    <!-- 子组件 -->
    <titleInsert @callback="getPageData" ref="titleInsert"></titleInsert>
  </div>
</template>

<script>
import { titlePage, deleteTitle } from '../../api/title'
import Pagination from '../../components/Pagination'
import { loadToken } from '../../utils/util'
import titleInsert from './titleInsert'

export default {
  data() {
    return {
      loading: false,
      formInline: {
        current: 1,
        size: 10,
        title: undefined,   // 标题
        token: loadToken(),
        orders: [],
      },
      userEntity: undefined,
      listData: [], //分页数据
      multipleSelection: [], //多选数据
      // page param
      pageParam: {
        currentPage: 1,
        pageSize: 10,
        total: 2
      }
    }
  },
  components: {
    Pagination,
    titleInsert
  },

  created() {
    // 加载用户缓存
    this.userEntity = JSON.parse(localStorage.getItem('userEntity'));
    // 分页数据
    this.getPageData()
  },
  methods: {
    // page-1: page
    getPageData(parameter) {
      this.loading = true
      if (!parameter){
        parameter = this.formInline;
      }
      titlePage(parameter)
        .then(res => {
          this.loading = false;
          this.listData = res.data.records
          this.pageParam.currentPage = res.data.current;
          this.pageParam.pageSize = res.data.size
          this.pageParam.total = res.data.total
        })
    },
    // page-2: callBack
    callback_getPageData(parm) {
      this.formInline.current = parm.currentPage
      this.formInline.size = parm.pageSize
      this.getPageData()
    },
    // page-3: page && sort
    getSortPageData(column){
      if (column.order != null){
        let sortProp = column.prop.replace(/([A-Z])/g, "_$1").toLowerCase();
        let orderBy = {"column": sortProp, "asc": column.order == 'ascending'};
        this.formInline.orders[0] = orderBy;
      } else {
        this.formInline.orders = [];
      }
      this.getPageData();
    },
    // search-1
    search() {
      this.getPageData()
    },
    // search-2
    resetSearch() {
      this.formInline.title = undefined     // 标题
    },
    handleAdd() {
      this.$refs.titleInsert.show();
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该标题, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTitle({idList: [row.title], token: loadToken()})
          .then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getPageData()
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handleBatchDelete() {
      this.$confirm('确认删除选中的标题?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const idList = this.multipleSelection.map(item => item.title);
        deleteTitle({idList: idList, token: loadToken()})
          .then(res => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.getPageData();
          })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    }
  }
}
</script>

<style scoped>
.user-search {
  margin-top: 20px;
}
</style>
