<template>
  <div>
    <el-tabs v-model="activeTab" class="margin-top">
      <el-tab-pane label="标题关键词" name="titleTab">
        <title-component />
      </el-tab-pane>
      <el-tab-pane label="部门关键词" name="deptTab">
        <dept-component />
      </el-tab-pane>
      <el-tab-pane label="职位关键词" name="positionTab">
        <position-component />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import TitleComponent from './title'
import DeptComponent from './dept'
import PositionComponent from '../position/position'

export default {
  name: 'GoogleParam',
  components: {
    TitleComponent,
    DeptComponent,
    PositionComponent
  },
  data() {
    return {
      activeTab: 'titleTab'
    }
  }
}
</script>

<style scoped>
.margin-top {
  margin-top: 0px;
}

.el-card__body, 
.el-main {
    padding: 0px !important;
}
</style>
