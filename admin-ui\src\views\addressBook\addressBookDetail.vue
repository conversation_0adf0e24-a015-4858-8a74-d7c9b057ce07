<template>
  <!-- 查看 -->
  <el-dialog title="通讯录详情" top="3vh" :visible.sync="detailFormVisible" width="600px" @click="closeDialog" class="addressbook-detail-dialog">
    <div class="detail-content">
      <el-form label-width="100px" :model="detailForm" ref="detailForm">
        <el-form-item label="编号">
          <span class="detail-value">{{ detailForm.id }}</span>
        </el-form-item>
        <el-form-item label="学校">
          <span class="detail-value">{{ getSchoolName(detailForm.schoolId) }}</span>
        </el-form-item>
        <!-- <el-form-item label="学校网址" v-if="detailForm.schoolWebsite">
          <el-link type="primary" :href="detailForm.schoolWebsite" target="_blank">
            {{ detailForm.schoolWebsite }}
          </el-link>
        </el-form-item> -->
        <el-form-item label="部门">
          <span class="detail-value">{{ detailForm.department || '无' }}</span>
        </el-form-item>
        <el-form-item label="联系人姓名">
          <span class="detail-value">{{ detailForm.contactName }}</span>
        </el-form-item>
        <el-form-item label="邮箱">
          <span class="detail-value">{{ detailForm.email || '无' }}</span>
        </el-form-item>
        <el-form-item label="电话">
          <span class="detail-value">{{ detailForm.phone || '无' }}</span>
        </el-form-item>
        <el-form-item label="职位">
          <span class="detail-value">{{ detailForm.position || '无' }}</span>
        </el-form-item>
        <el-form-item label="自动获取">
          <el-tag :type="!detailForm.manualFlag ? 'success' : 'info'">
            {{ !detailForm.manualFlag ? '是' : '否' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="更新时间">
          <span class="detail-value">{{ detailForm.updateTime }}</span>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getSchoolList } from '../../api/addressBook'

export default {
  name: 'addressBookDetail',
  props: ['childMsg'],
  data() {
    return {
      detailFormVisible: false,
      detailForm: {
        id: '',
        schoolId: '',
        schoolName: '',
        schoolWebsite: '',
        department: '',
        contactName: '',
        email: '',
        phone: '',
        position: '',
        manualFlag: true,
        updateTime: ''
      },
      schoolList: []
    }
  },
  methods: {
    closeDialog() {
      this.detailFormVisible = false
    },
    show(row) {
      this.detailFormVisible = true;
      this.resetForm();
      if (row) {
        Object.assign(this.detailForm, row);
        // 确保学校列表已加载
        this.loadSchoolList(row.schoolId);
      }
    },
    resetForm() {
      this.detailForm = {
        id: '',
        schoolId: '',
        schoolName: '',
        schoolWebsite: '',
        department: '',
        contactName: '',
        email: '',
        phone: '',
        position: '',
        manualFlag: true,
        updateTime: ''
      };
    },
    // 加载学校列表
    loadSchoolList(schoolId) {
      if (this.schoolList.length === 0) {
        getSchoolList()
          .then(res => {
            this.schoolList = res.data;
            // 加载完成后设置学校网址
            if (schoolId) {
              const school = this.schoolList.find(s => s.schoolId === schoolId);
              if (school) {
                this.detailForm.schoolWebsite = school.website;
              }
            }
          })
      } else {
        // 如果已经加载过，直接设置学校网址
        if (schoolId) {
          const school = this.schoolList.find(s => s.schoolId === schoolId);
          if (school) {
            this.detailForm.schoolWebsite = school.website;
          }
        }
      }
    },
    getSchoolWebsite(schoolId) {
      if (this.schoolList.length === 0) {
        getSchoolList()
          .then(res => {
            this.schoolList = res.data;
            const school = this.schoolList.find(s => s.schoolId === schoolId);
            if (school) {
              this.detailForm.schoolWebsite = school.website;
            }
          })
      } else {
        const school = this.schoolList.find(s => s.schoolId === schoolId);
        if (school) {
          this.detailForm.schoolWebsite = school.website;
        }
      }
    },
    // 获取学校英文名称
    getSchoolName(schoolId) {
      const school = this.schoolList.find(s => s.schoolId === schoolId);
      return school ? school.englishName : '未知学校';
    }
  }
}
</script>

<style>
.el-dialog .el-dialog__body {
  padding: 0px 20px !important;
}

.addressbook-detail-dialog .el-dialog__body {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  padding: 20px !important;
}

.detail-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.detail-content .el-form-item {
  margin-bottom: 18px;
}

.detail-content .el-form-item__label {
  font-weight: 600;
  color: #606266;
  padding-top: 8px;
}

.detail-value {
  color: #303133;
  font-size: 14px;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  display: inline-block;
  min-width: 200px;
  vertical-align: middle;
}
</style>
